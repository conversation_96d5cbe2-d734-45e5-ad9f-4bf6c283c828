# pylint: disable=too-many-lines,too-many-statements
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
import datetime
import sys
from typing import Any, Callable, Dict, IO, Literal, Optional, Type, TypeVar, Union

from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import HttpResponse
from azure.core.rest import HttpRequest
from azure.core.tracing.decorator import distributed_trace
from azure.core.utils import case_insensitive_dict

from .. import models as _models
from .._serialization import Serializer
from .._vendor import _convert_request

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore  # pylint: disable=ungrouped-imports
T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, HttpResponse], T, Dict[str, Any]], Any]]

_SERIALIZER = Serializer()
_SERIALIZER.client_side_validation = False


def build_upload_request(
    url: str,
    *,
    content_length: int,
    content: IO[bytes],
    timeout: Optional[int] = None,
    transactional_content_md5: Optional[bytes] = None,
    blob_content_type: Optional[str] = None,
    blob_content_encoding: Optional[str] = None,
    blob_content_language: Optional[str] = None,
    blob_content_md5: Optional[bytes] = None,
    blob_cache_control: Optional[str] = None,
    metadata: Optional[Dict[str, str]] = None,
    lease_id: Optional[str] = None,
    blob_content_disposition: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    tier: Optional[Union[str, _models.AccessTierOptional]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    blob_tags_string: Optional[str] = None,
    immutability_policy_expiry: Optional[datetime.datetime] = None,
    immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
    legal_hold: Optional[bool] = None,
    transactional_content_crc64: Optional[bytes] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    blob_type: Literal["BlockBlob"] = kwargs.pop("blob_type", _headers.pop("x-ms-blob-type", "BlockBlob"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-blob-type"] = _SERIALIZER.header("blob_type", blob_type, "str")
    if transactional_content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header(
            "transactional_content_md5", transactional_content_md5, "bytearray"
        )
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    if blob_content_type is not None:
        _headers["x-ms-blob-content-type"] = _SERIALIZER.header("blob_content_type", blob_content_type, "str")
    if blob_content_encoding is not None:
        _headers["x-ms-blob-content-encoding"] = _SERIALIZER.header(
            "blob_content_encoding", blob_content_encoding, "str"
        )
    if blob_content_language is not None:
        _headers["x-ms-blob-content-language"] = _SERIALIZER.header(
            "blob_content_language", blob_content_language, "str"
        )
    if blob_content_md5 is not None:
        _headers["x-ms-blob-content-md5"] = _SERIALIZER.header("blob_content_md5", blob_content_md5, "bytearray")
    if blob_cache_control is not None:
        _headers["x-ms-blob-cache-control"] = _SERIALIZER.header("blob_cache_control", blob_cache_control, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if blob_content_disposition is not None:
        _headers["x-ms-blob-content-disposition"] = _SERIALIZER.header(
            "blob_content_disposition", blob_content_disposition, "str"
        )
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if tier is not None:
        _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if blob_tags_string is not None:
        _headers["x-ms-tags"] = _SERIALIZER.header("blob_tags_string", blob_tags_string, "str")
    if immutability_policy_expiry is not None:
        _headers["x-ms-immutability-policy-until-date"] = _SERIALIZER.header(
            "immutability_policy_expiry", immutability_policy_expiry, "rfc-1123"
        )
    if immutability_policy_mode is not None:
        _headers["x-ms-immutability-policy-mode"] = _SERIALIZER.header(
            "immutability_policy_mode", immutability_policy_mode, "str"
        )
    if legal_hold is not None:
        _headers["x-ms-legal-hold"] = _SERIALIZER.header("legal_hold", legal_hold, "bool")
    if transactional_content_crc64 is not None:
        _headers["x-ms-content-crc64"] = _SERIALIZER.header(
            "transactional_content_crc64", transactional_content_crc64, "bytearray"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, content=content, **kwargs)


def build_put_blob_from_url_request(
    url: str,
    *,
    content_length: int,
    copy_source: str,
    timeout: Optional[int] = None,
    transactional_content_md5: Optional[bytes] = None,
    blob_content_type: Optional[str] = None,
    blob_content_encoding: Optional[str] = None,
    blob_content_language: Optional[str] = None,
    blob_content_md5: Optional[bytes] = None,
    blob_cache_control: Optional[str] = None,
    metadata: Optional[Dict[str, str]] = None,
    lease_id: Optional[str] = None,
    blob_content_disposition: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    tier: Optional[Union[str, _models.AccessTierOptional]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    source_if_modified_since: Optional[datetime.datetime] = None,
    source_if_unmodified_since: Optional[datetime.datetime] = None,
    source_if_match: Optional[str] = None,
    source_if_none_match: Optional[str] = None,
    source_if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    source_content_md5: Optional[bytes] = None,
    blob_tags_string: Optional[str] = None,
    copy_source_blob_properties: Optional[bool] = None,
    copy_source_authorization: Optional[str] = None,
    copy_source_tags: Optional[Union[str, _models.BlobCopySourceTags]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    blob_type: Literal["BlockBlob"] = kwargs.pop("blob_type", _headers.pop("x-ms-blob-type", "BlockBlob"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-blob-type"] = _SERIALIZER.header("blob_type", blob_type, "str")
    if transactional_content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header(
            "transactional_content_md5", transactional_content_md5, "bytearray"
        )
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    if blob_content_type is not None:
        _headers["x-ms-blob-content-type"] = _SERIALIZER.header("blob_content_type", blob_content_type, "str")
    if blob_content_encoding is not None:
        _headers["x-ms-blob-content-encoding"] = _SERIALIZER.header(
            "blob_content_encoding", blob_content_encoding, "str"
        )
    if blob_content_language is not None:
        _headers["x-ms-blob-content-language"] = _SERIALIZER.header(
            "blob_content_language", blob_content_language, "str"
        )
    if blob_content_md5 is not None:
        _headers["x-ms-blob-content-md5"] = _SERIALIZER.header("blob_content_md5", blob_content_md5, "bytearray")
    if blob_cache_control is not None:
        _headers["x-ms-blob-cache-control"] = _SERIALIZER.header("blob_cache_control", blob_cache_control, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if blob_content_disposition is not None:
        _headers["x-ms-blob-content-disposition"] = _SERIALIZER.header(
            "blob_content_disposition", blob_content_disposition, "str"
        )
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if tier is not None:
        _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    if source_if_modified_since is not None:
        _headers["x-ms-source-if-modified-since"] = _SERIALIZER.header(
            "source_if_modified_since", source_if_modified_since, "rfc-1123"
        )
    if source_if_unmodified_since is not None:
        _headers["x-ms-source-if-unmodified-since"] = _SERIALIZER.header(
            "source_if_unmodified_since", source_if_unmodified_since, "rfc-1123"
        )
    if source_if_match is not None:
        _headers["x-ms-source-if-match"] = _SERIALIZER.header("source_if_match", source_if_match, "str")
    if source_if_none_match is not None:
        _headers["x-ms-source-if-none-match"] = _SERIALIZER.header("source_if_none_match", source_if_none_match, "str")
    if source_if_tags is not None:
        _headers["x-ms-source-if-tags"] = _SERIALIZER.header("source_if_tags", source_if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if source_content_md5 is not None:
        _headers["x-ms-source-content-md5"] = _SERIALIZER.header("source_content_md5", source_content_md5, "bytearray")
    if blob_tags_string is not None:
        _headers["x-ms-tags"] = _SERIALIZER.header("blob_tags_string", blob_tags_string, "str")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("copy_source", copy_source, "str")
    if copy_source_blob_properties is not None:
        _headers["x-ms-copy-source-blob-properties"] = _SERIALIZER.header(
            "copy_source_blob_properties", copy_source_blob_properties, "bool"
        )
    if copy_source_authorization is not None:
        _headers["x-ms-copy-source-authorization"] = _SERIALIZER.header(
            "copy_source_authorization", copy_source_authorization, "str"
        )
    if copy_source_tags is not None:
        _headers["x-ms-copy-source-tag-option"] = _SERIALIZER.header("copy_source_tags", copy_source_tags, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_stage_block_request(
    url: str,
    *,
    block_id: str,
    content_length: int,
    content: IO[bytes],
    transactional_content_md5: Optional[bytes] = None,
    transactional_content_crc64: Optional[bytes] = None,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["block"] = kwargs.pop("comp", _params.pop("comp", "block"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    _params["blockid"] = _SERIALIZER.query("block_id", block_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    if transactional_content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header(
            "transactional_content_md5", transactional_content_md5, "bytearray"
        )
    if transactional_content_crc64 is not None:
        _headers["x-ms-content-crc64"] = _SERIALIZER.header(
            "transactional_content_crc64", transactional_content_crc64, "bytearray"
        )
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, content=content, **kwargs)


def build_stage_block_from_url_request(
    url: str,
    *,
    block_id: str,
    content_length: int,
    source_url: str,
    source_range: Optional[str] = None,
    source_content_md5: Optional[bytes] = None,
    source_contentcrc64: Optional[bytes] = None,
    timeout: Optional[int] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    lease_id: Optional[str] = None,
    source_if_modified_since: Optional[datetime.datetime] = None,
    source_if_unmodified_since: Optional[datetime.datetime] = None,
    source_if_match: Optional[str] = None,
    source_if_none_match: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    copy_source_authorization: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["block"] = kwargs.pop("comp", _params.pop("comp", "block"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    _params["blockid"] = _SERIALIZER.query("block_id", block_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("source_url", source_url, "str")
    if source_range is not None:
        _headers["x-ms-source-range"] = _SERIALIZER.header("source_range", source_range, "str")
    if source_content_md5 is not None:
        _headers["x-ms-source-content-md5"] = _SERIALIZER.header("source_content_md5", source_content_md5, "bytearray")
    if source_contentcrc64 is not None:
        _headers["x-ms-source-content-crc64"] = _SERIALIZER.header(
            "source_contentcrc64", source_contentcrc64, "bytearray"
        )
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if source_if_modified_since is not None:
        _headers["x-ms-source-if-modified-since"] = _SERIALIZER.header(
            "source_if_modified_since", source_if_modified_since, "rfc-1123"
        )
    if source_if_unmodified_since is not None:
        _headers["x-ms-source-if-unmodified-since"] = _SERIALIZER.header(
            "source_if_unmodified_since", source_if_unmodified_since, "rfc-1123"
        )
    if source_if_match is not None:
        _headers["x-ms-source-if-match"] = _SERIALIZER.header("source_if_match", source_if_match, "str")
    if source_if_none_match is not None:
        _headers["x-ms-source-if-none-match"] = _SERIALIZER.header("source_if_none_match", source_if_none_match, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if copy_source_authorization is not None:
        _headers["x-ms-copy-source-authorization"] = _SERIALIZER.header(
            "copy_source_authorization", copy_source_authorization, "str"
        )
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_commit_block_list_request(
    url: str,
    *,
    content: Any,
    timeout: Optional[int] = None,
    blob_cache_control: Optional[str] = None,
    blob_content_type: Optional[str] = None,
    blob_content_encoding: Optional[str] = None,
    blob_content_language: Optional[str] = None,
    blob_content_md5: Optional[bytes] = None,
    transactional_content_md5: Optional[bytes] = None,
    transactional_content_crc64: Optional[bytes] = None,
    metadata: Optional[Dict[str, str]] = None,
    lease_id: Optional[str] = None,
    blob_content_disposition: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    tier: Optional[Union[str, _models.AccessTierOptional]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    blob_tags_string: Optional[str] = None,
    immutability_policy_expiry: Optional[datetime.datetime] = None,
    immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
    legal_hold: Optional[bool] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["blocklist"] = kwargs.pop("comp", _params.pop("comp", "blocklist"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if blob_cache_control is not None:
        _headers["x-ms-blob-cache-control"] = _SERIALIZER.header("blob_cache_control", blob_cache_control, "str")
    if blob_content_type is not None:
        _headers["x-ms-blob-content-type"] = _SERIALIZER.header("blob_content_type", blob_content_type, "str")
    if blob_content_encoding is not None:
        _headers["x-ms-blob-content-encoding"] = _SERIALIZER.header(
            "blob_content_encoding", blob_content_encoding, "str"
        )
    if blob_content_language is not None:
        _headers["x-ms-blob-content-language"] = _SERIALIZER.header(
            "blob_content_language", blob_content_language, "str"
        )
    if blob_content_md5 is not None:
        _headers["x-ms-blob-content-md5"] = _SERIALIZER.header("blob_content_md5", blob_content_md5, "bytearray")
    if transactional_content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header(
            "transactional_content_md5", transactional_content_md5, "bytearray"
        )
    if transactional_content_crc64 is not None:
        _headers["x-ms-content-crc64"] = _SERIALIZER.header(
            "transactional_content_crc64", transactional_content_crc64, "bytearray"
        )
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if blob_content_disposition is not None:
        _headers["x-ms-blob-content-disposition"] = _SERIALIZER.header(
            "blob_content_disposition", blob_content_disposition, "str"
        )
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if tier is not None:
        _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if blob_tags_string is not None:
        _headers["x-ms-tags"] = _SERIALIZER.header("blob_tags_string", blob_tags_string, "str")
    if immutability_policy_expiry is not None:
        _headers["x-ms-immutability-policy-until-date"] = _SERIALIZER.header(
            "immutability_policy_expiry", immutability_policy_expiry, "rfc-1123"
        )
    if immutability_policy_mode is not None:
        _headers["x-ms-immutability-policy-mode"] = _SERIALIZER.header(
            "immutability_policy_mode", immutability_policy_mode, "str"
        )
    if legal_hold is not None:
        _headers["x-ms-legal-hold"] = _SERIALIZER.header("legal_hold", legal_hold, "bool")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, content=content, **kwargs)


def build_get_block_list_request(
    url: str,
    *,
    snapshot: Optional[str] = None,
    list_type: Union[str, _models.BlockListType] = "committed",
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["blocklist"] = kwargs.pop("comp", _params.pop("comp", "blocklist"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    _params["blocklisttype"] = _SERIALIZER.query("list_type", list_type, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


class BlockBlobOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.storage.blob.AzureBlobStorage`'s
        :attr:`block_blob` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs):
        input_args = list(args)
        self._client = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @distributed_trace
    def upload(  # pylint: disable=inconsistent-return-statements
        self,
        content_length: int,
        body: IO[bytes],
        timeout: Optional[int] = None,
        transactional_content_md5: Optional[bytes] = None,
        metadata: Optional[Dict[str, str]] = None,
        tier: Optional[Union[str, _models.AccessTierOptional]] = None,
        request_id_parameter: Optional[str] = None,
        blob_tags_string: Optional[str] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        legal_hold: Optional[bool] = None,
        transactional_content_crc64: Optional[bytes] = None,
        blob_http_headers: Optional[_models.BlobHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Upload Block Blob operation updates the content of an existing block blob. Updating an
        existing block blob overwrites any existing metadata on the blob. Partial updates are not
        supported with Put Blob; the content of the existing blob is overwritten with the content of
        the new blob. To perform a partial update of the content of a block blob, use the Put Block
        List operation.

        :param content_length: The length of the request. Required.
        :type content_length: int
        :param body: Initial data. Required.
        :type body: IO[bytes]
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param tier: Optional. Indicates the tier to be set on the blob. Known values are: "P4", "P6",
         "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and
         "Cold". Default value is None.
        :type tier: str or ~azure.storage.blob.models.AccessTierOptional
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param legal_hold: Specified if a legal hold should be set on the blob. Default value is None.
        :type legal_hold: bool
        :param transactional_content_crc64: Specify the transactional crc64 for the body, to be
         validated by the service. Default value is None.
        :type transactional_content_crc64: bytes
        :param blob_http_headers: Parameter group. Default value is None.
        :type blob_http_headers: ~azure.storage.blob.models.BlobHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        blob_type: Literal["BlockBlob"] = kwargs.pop("blob_type", _headers.pop("x-ms-blob-type", "BlockBlob"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/octet-stream"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _blob_content_type = None
        _blob_content_encoding = None
        _blob_content_language = None
        _blob_content_md5 = None
        _blob_cache_control = None
        _lease_id = None
        _blob_content_disposition = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if blob_http_headers is not None:
            _blob_cache_control = blob_http_headers.blob_cache_control
            _blob_content_disposition = blob_http_headers.blob_content_disposition
            _blob_content_encoding = blob_http_headers.blob_content_encoding
            _blob_content_language = blob_http_headers.blob_content_language
            _blob_content_md5 = blob_http_headers.blob_content_md5
            _blob_content_type = blob_http_headers.blob_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        _content = body

        _request = build_upload_request(
            url=self._config.url,
            content_length=content_length,
            timeout=timeout,
            transactional_content_md5=transactional_content_md5,
            blob_content_type=_blob_content_type,
            blob_content_encoding=_blob_content_encoding,
            blob_content_language=_blob_content_language,
            blob_content_md5=_blob_content_md5,
            blob_cache_control=_blob_cache_control,
            metadata=metadata,
            lease_id=_lease_id,
            blob_content_disposition=_blob_content_disposition,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            tier=tier,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            blob_tags_string=blob_tags_string,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            legal_hold=legal_hold,
            transactional_content_crc64=transactional_content_crc64,
            blob_type=blob_type,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def put_blob_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        content_length: int,
        copy_source: str,
        timeout: Optional[int] = None,
        transactional_content_md5: Optional[bytes] = None,
        metadata: Optional[Dict[str, str]] = None,
        tier: Optional[Union[str, _models.AccessTierOptional]] = None,
        request_id_parameter: Optional[str] = None,
        source_content_md5: Optional[bytes] = None,
        blob_tags_string: Optional[str] = None,
        copy_source_blob_properties: Optional[bool] = None,
        copy_source_authorization: Optional[str] = None,
        copy_source_tags: Optional[Union[str, _models.BlobCopySourceTags]] = None,
        blob_http_headers: Optional[_models.BlobHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Put Blob from URL operation creates a new Block Blob where the contents of the blob are
        read from a given URL.  This API is supported beginning with the 2020-04-08 version. Partial
        updates are not supported with Put Blob from URL; the content of an existing blob is
        overwritten with the content of the new blob.  To perform partial updates to a block blob’s
        contents using a source URL, use the Put Block from URL API in conjunction with Put Block List.

        :param content_length: The length of the request. Required.
        :type content_length: int
        :param copy_source: Specifies the name of the source page blob snapshot. This value is a URL of
         up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it
         would appear in a request URI. The source blob must either be public or must be authenticated
         via a shared access signature. Required.
        :type copy_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param tier: Optional. Indicates the tier to be set on the blob. Known values are: "P4", "P6",
         "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and
         "Cold". Default value is None.
        :type tier: str or ~azure.storage.blob.models.AccessTierOptional
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param source_content_md5: Specify the md5 calculated for the range of bytes that must be read
         from the copy source. Default value is None.
        :type source_content_md5: bytes
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param copy_source_blob_properties: Optional, default is true.  Indicates if properties from
         the source blob should be copied. Default value is None.
        :type copy_source_blob_properties: bool
        :param copy_source_authorization: Only Bearer type is supported. Credentials should be a valid
         OAuth access token to copy source. Default value is None.
        :type copy_source_authorization: str
        :param copy_source_tags: Optional, default 'replace'.  Indicates if source tags should be
         copied or replaced with the tags specified by x-ms-tags. Known values are: "REPLACE" and
         "COPY". Default value is None.
        :type copy_source_tags: str or ~azure.storage.blob.models.BlobCopySourceTags
        :param blob_http_headers: Parameter group. Default value is None.
        :type blob_http_headers: ~azure.storage.blob.models.BlobHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.blob.models.SourceModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        blob_type: Literal["BlockBlob"] = kwargs.pop("blob_type", _headers.pop("x-ms-blob-type", "BlockBlob"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _blob_content_type = None
        _blob_content_encoding = None
        _blob_content_language = None
        _blob_content_md5 = None
        _blob_cache_control = None
        _lease_id = None
        _blob_content_disposition = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _source_if_modified_since = None
        _source_if_unmodified_since = None
        _source_if_match = None
        _source_if_none_match = None
        _source_if_tags = None
        if blob_http_headers is not None:
            _blob_cache_control = blob_http_headers.blob_cache_control
            _blob_content_disposition = blob_http_headers.blob_content_disposition
            _blob_content_encoding = blob_http_headers.blob_content_encoding
            _blob_content_language = blob_http_headers.blob_content_language
            _blob_content_md5 = blob_http_headers.blob_content_md5
            _blob_content_type = blob_http_headers.blob_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if source_modified_access_conditions is not None:
            _source_if_match = source_modified_access_conditions.source_if_match
            _source_if_modified_since = source_modified_access_conditions.source_if_modified_since
            _source_if_none_match = source_modified_access_conditions.source_if_none_match
            _source_if_tags = source_modified_access_conditions.source_if_tags
            _source_if_unmodified_since = source_modified_access_conditions.source_if_unmodified_since

        _request = build_put_blob_from_url_request(
            url=self._config.url,
            content_length=content_length,
            copy_source=copy_source,
            timeout=timeout,
            transactional_content_md5=transactional_content_md5,
            blob_content_type=_blob_content_type,
            blob_content_encoding=_blob_content_encoding,
            blob_content_language=_blob_content_language,
            blob_content_md5=_blob_content_md5,
            blob_cache_control=_blob_cache_control,
            metadata=metadata,
            lease_id=_lease_id,
            blob_content_disposition=_blob_content_disposition,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            tier=tier,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            source_if_modified_since=_source_if_modified_since,
            source_if_unmodified_since=_source_if_unmodified_since,
            source_if_match=_source_if_match,
            source_if_none_match=_source_if_none_match,
            source_if_tags=_source_if_tags,
            request_id_parameter=request_id_parameter,
            source_content_md5=source_content_md5,
            blob_tags_string=blob_tags_string,
            copy_source_blob_properties=copy_source_blob_properties,
            copy_source_authorization=copy_source_authorization,
            copy_source_tags=copy_source_tags,
            blob_type=blob_type,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def stage_block(  # pylint: disable=inconsistent-return-statements
        self,
        block_id: str,
        content_length: int,
        body: IO[bytes],
        transactional_content_md5: Optional[bytes] = None,
        transactional_content_crc64: Optional[bytes] = None,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        **kwargs: Any
    ) -> None:
        """The Stage Block operation creates a new block to be committed as part of a blob.

        :param block_id: A valid Base64 string value that identifies the block. Prior to encoding, the
         string must be less than or equal to 64 bytes in size. For a given blob, the length of the
         value specified for the blockid parameter must be the same size for each block. Required.
        :type block_id: str
        :param content_length: The length of the request. Required.
        :type content_length: int
        :param body: Initial data. Required.
        :type body: IO[bytes]
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param transactional_content_crc64: Specify the transactional crc64 for the body, to be
         validated by the service. Default value is None.
        :type transactional_content_crc64: bytes
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["block"] = kwargs.pop("comp", _params.pop("comp", "block"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/octet-stream"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        _content = body

        _request = build_stage_block_request(
            url=self._config.url,
            block_id=block_id,
            content_length=content_length,
            transactional_content_md5=transactional_content_md5,
            transactional_content_crc64=transactional_content_crc64,
            timeout=timeout,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            request_id_parameter=request_id_parameter,
            comp=comp,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def stage_block_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        block_id: str,
        content_length: int,
        source_url: str,
        source_range: Optional[str] = None,
        source_content_md5: Optional[bytes] = None,
        source_contentcrc64: Optional[bytes] = None,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        copy_source_authorization: Optional[str] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Stage Block operation creates a new block to be committed as part of a blob where the
        contents are read from a URL.

        :param block_id: A valid Base64 string value that identifies the block. Prior to encoding, the
         string must be less than or equal to 64 bytes in size. For a given blob, the length of the
         value specified for the blockid parameter must be the same size for each block. Required.
        :type block_id: str
        :param content_length: The length of the request. Required.
        :type content_length: int
        :param source_url: Specify a URL to the copy source. Required.
        :type source_url: str
        :param source_range: Bytes of source data in the specified range. Default value is None.
        :type source_range: str
        :param source_content_md5: Specify the md5 calculated for the range of bytes that must be read
         from the copy source. Default value is None.
        :type source_content_md5: bytes
        :param source_contentcrc64: Specify the crc64 calculated for the range of bytes that must be
         read from the copy source. Default value is None.
        :type source_contentcrc64: bytes
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param copy_source_authorization: Only Bearer type is supported. Credentials should be a valid
         OAuth access token to copy source. Default value is None.
        :type copy_source_authorization: str
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.blob.models.SourceModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["block"] = kwargs.pop("comp", _params.pop("comp", "block"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _lease_id = None
        _source_if_modified_since = None
        _source_if_unmodified_since = None
        _source_if_match = None
        _source_if_none_match = None
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if source_modified_access_conditions is not None:
            _source_if_match = source_modified_access_conditions.source_if_match
            _source_if_modified_since = source_modified_access_conditions.source_if_modified_since
            _source_if_none_match = source_modified_access_conditions.source_if_none_match
            _source_if_unmodified_since = source_modified_access_conditions.source_if_unmodified_since

        _request = build_stage_block_from_url_request(
            url=self._config.url,
            block_id=block_id,
            content_length=content_length,
            source_url=source_url,
            source_range=source_range,
            source_content_md5=source_content_md5,
            source_contentcrc64=source_contentcrc64,
            timeout=timeout,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            lease_id=_lease_id,
            source_if_modified_since=_source_if_modified_since,
            source_if_unmodified_since=_source_if_unmodified_since,
            source_if_match=_source_if_match,
            source_if_none_match=_source_if_none_match,
            request_id_parameter=request_id_parameter,
            copy_source_authorization=copy_source_authorization,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def commit_block_list(  # pylint: disable=inconsistent-return-statements
        self,
        blocks: _models.BlockLookupList,
        timeout: Optional[int] = None,
        transactional_content_md5: Optional[bytes] = None,
        transactional_content_crc64: Optional[bytes] = None,
        metadata: Optional[Dict[str, str]] = None,
        tier: Optional[Union[str, _models.AccessTierOptional]] = None,
        request_id_parameter: Optional[str] = None,
        blob_tags_string: Optional[str] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        legal_hold: Optional[bool] = None,
        blob_http_headers: Optional[_models.BlobHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Commit Block List operation writes a blob by specifying the list of block IDs that make up
        the blob. In order to be written as part of a blob, a block must have been successfully written
        to the server in a prior Put Block operation. You can call Put Block List to update a blob by
        uploading only those blocks that have changed, then committing the new and existing blocks
        together. You can do this by specifying whether to commit a block from the committed block list
        or from the uncommitted block list, or to commit the most recently uploaded version of the
        block, whichever list it may belong to.

        :param blocks: Blob Blocks. Required.
        :type blocks: ~azure.storage.blob.models.BlockLookupList
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param transactional_content_crc64: Specify the transactional crc64 for the body, to be
         validated by the service. Default value is None.
        :type transactional_content_crc64: bytes
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param tier: Optional. Indicates the tier to be set on the blob. Known values are: "P4", "P6",
         "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and
         "Cold". Default value is None.
        :type tier: str or ~azure.storage.blob.models.AccessTierOptional
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param legal_hold: Specified if a legal hold should be set on the blob. Default value is None.
        :type legal_hold: bool
        :param blob_http_headers: Parameter group. Default value is None.
        :type blob_http_headers: ~azure.storage.blob.models.BlobHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["blocklist"] = kwargs.pop("comp", _params.pop("comp", "blocklist"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/xml"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _blob_cache_control = None
        _blob_content_type = None
        _blob_content_encoding = None
        _blob_content_language = None
        _blob_content_md5 = None
        _lease_id = None
        _blob_content_disposition = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if blob_http_headers is not None:
            _blob_cache_control = blob_http_headers.blob_cache_control
            _blob_content_disposition = blob_http_headers.blob_content_disposition
            _blob_content_encoding = blob_http_headers.blob_content_encoding
            _blob_content_language = blob_http_headers.blob_content_language
            _blob_content_md5 = blob_http_headers.blob_content_md5
            _blob_content_type = blob_http_headers.blob_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        _content = self._serialize.body(blocks, "BlockLookupList", is_xml=True)

        _request = build_commit_block_list_request(
            url=self._config.url,
            timeout=timeout,
            blob_cache_control=_blob_cache_control,
            blob_content_type=_blob_content_type,
            blob_content_encoding=_blob_content_encoding,
            blob_content_language=_blob_content_language,
            blob_content_md5=_blob_content_md5,
            transactional_content_md5=transactional_content_md5,
            transactional_content_crc64=transactional_content_crc64,
            metadata=metadata,
            lease_id=_lease_id,
            blob_content_disposition=_blob_content_disposition,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            tier=tier,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            blob_tags_string=blob_tags_string,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            legal_hold=legal_hold,
            comp=comp,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def get_block_list(
        self,
        snapshot: Optional[str] = None,
        list_type: Union[str, _models.BlockListType] = "committed",
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> _models.BlockList:
        """The Get Block List operation retrieves the list of blocks that have been uploaded as part of a
        block blob.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param list_type: Specifies whether to return the list of committed blocks, the list of
         uncommitted blocks, or both lists together. Known values are: "committed", "uncommitted", and
         "all". Default value is "committed".
        :type list_type: str or ~azure.storage.blob.models.BlockListType
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: BlockList or the result of cls(response)
        :rtype: ~azure.storage.blob.models.BlockList
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["blocklist"] = kwargs.pop("comp", _params.pop("comp", "blocklist"))
        cls: ClsType[_models.BlockList] = kwargs.pop("cls", None)

        _lease_id = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_tags = modified_access_conditions.if_tags

        _request = build_get_block_list_request(
            url=self._config.url,
            snapshot=snapshot,
            list_type=list_type,
            timeout=timeout,
            lease_id=_lease_id,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
        response_headers["x-ms-blob-content-length"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-content-length")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("BlockList", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore
