# pylint: disable=too-many-lines,too-many-statements
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
import datetime
import sys
from typing import Any, Callable, Dict, Iterator, Literal, Optional, Type, TypeVar, Union

from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import HttpResponse
from azure.core.rest import HttpRequest
from azure.core.tracing.decorator import distributed_trace
from azure.core.utils import case_insensitive_dict

from .. import models as _models
from .._serialization import Serializer
from .._vendor import _convert_request

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore  # pylint: disable=ungrouped-imports
T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, HttpResponse], T, Dict[str, Any]], Any]]

_SERIALIZER = Serializer()
_SERIALIZER.client_side_validation = False


def build_download_request(
    url: str,
    *,
    snapshot: Optional[str] = None,
    version_id: Optional[str] = None,
    timeout: Optional[int] = None,
    range: Optional[str] = None,
    lease_id: Optional[str] = None,
    range_get_content_md5: Optional[bool] = None,
    range_get_content_crc64: Optional[bool] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if range is not None:
        _headers["x-ms-range"] = _SERIALIZER.header("range", range, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if range_get_content_md5 is not None:
        _headers["x-ms-range-get-content-md5"] = _SERIALIZER.header(
            "range_get_content_md5", range_get_content_md5, "bool"
        )
    if range_get_content_crc64 is not None:
        _headers["x-ms-range-get-content-crc64"] = _SERIALIZER.header(
            "range_get_content_crc64", range_get_content_crc64, "bool"
        )
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_get_properties_request(
    url: str,
    *,
    snapshot: Optional[str] = None,
    version_id: Optional[str] = None,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="HEAD", url=_url, params=_params, headers=_headers, **kwargs)


def build_delete_request(
    url: str,
    *,
    snapshot: Optional[str] = None,
    version_id: Optional[str] = None,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    delete_snapshots: Optional[Union[str, _models.DeleteSnapshotsOptionType]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    blob_delete_type: Literal["Permanent"] = "Permanent",
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if blob_delete_type is not None:
        _params["deletetype"] = _SERIALIZER.query("blob_delete_type", blob_delete_type, "str")

    # Construct headers
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if delete_snapshots is not None:
        _headers["x-ms-delete-snapshots"] = _SERIALIZER.header("delete_snapshots", delete_snapshots, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="DELETE", url=_url, params=_params, headers=_headers, **kwargs)


def build_undelete_request(
    url: str, *, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["undelete"] = kwargs.pop("comp", _params.pop("comp", "undelete"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_expiry_request(
    url: str,
    *,
    expiry_options: Union[str, _models.BlobExpiryOptions],
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    expires_on: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["expiry"] = kwargs.pop("comp", _params.pop("comp", "expiry"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["x-ms-expiry-option"] = _SERIALIZER.header("expiry_options", expiry_options, "str")
    if expires_on is not None:
        _headers["x-ms-expiry-time"] = _SERIALIZER.header("expires_on", expires_on, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_http_headers_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    blob_cache_control: Optional[str] = None,
    blob_content_type: Optional[str] = None,
    blob_content_md5: Optional[bytes] = None,
    blob_content_encoding: Optional[str] = None,
    blob_content_language: Optional[str] = None,
    lease_id: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    blob_content_disposition: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if blob_cache_control is not None:
        _headers["x-ms-blob-cache-control"] = _SERIALIZER.header("blob_cache_control", blob_cache_control, "str")
    if blob_content_type is not None:
        _headers["x-ms-blob-content-type"] = _SERIALIZER.header("blob_content_type", blob_content_type, "str")
    if blob_content_md5 is not None:
        _headers["x-ms-blob-content-md5"] = _SERIALIZER.header("blob_content_md5", blob_content_md5, "bytearray")
    if blob_content_encoding is not None:
        _headers["x-ms-blob-content-encoding"] = _SERIALIZER.header(
            "blob_content_encoding", blob_content_encoding, "str"
        )
    if blob_content_language is not None:
        _headers["x-ms-blob-content-language"] = _SERIALIZER.header(
            "blob_content_language", blob_content_language, "str"
        )
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    if blob_content_disposition is not None:
        _headers["x-ms-blob-content-disposition"] = _SERIALIZER.header(
            "blob_content_disposition", blob_content_disposition, "str"
        )
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_immutability_policy_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    immutability_policy_expiry: Optional[datetime.datetime] = None,
    immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["immutabilityPolicies"] = kwargs.pop("comp", _params.pop("comp", "immutabilityPolicies"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if immutability_policy_expiry is not None:
        _headers["x-ms-immutability-policy-until-date"] = _SERIALIZER.header(
            "immutability_policy_expiry", immutability_policy_expiry, "rfc-1123"
        )
    if immutability_policy_mode is not None:
        _headers["x-ms-immutability-policy-mode"] = _SERIALIZER.header(
            "immutability_policy_mode", immutability_policy_mode, "str"
        )
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_delete_immutability_policy_request(
    url: str, *, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["immutabilityPolicies"] = kwargs.pop("comp", _params.pop("comp", "immutabilityPolicies"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="DELETE", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_legal_hold_request(
    url: str,
    *,
    legal_hold: bool,
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["legalhold"] = kwargs.pop("comp", _params.pop("comp", "legalhold"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["x-ms-legal-hold"] = _SERIALIZER.header("legal_hold", legal_hold, "bool")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_metadata_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    lease_id: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["metadata"] = kwargs.pop("comp", _params.pop("comp", "metadata"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_acquire_lease_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    duration: Optional[int] = None,
    proposed_lease_id: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["acquire"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "acquire"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    if duration is not None:
        _headers["x-ms-lease-duration"] = _SERIALIZER.header("duration", duration, "int")
    if proposed_lease_id is not None:
        _headers["x-ms-proposed-lease-id"] = _SERIALIZER.header("proposed_lease_id", proposed_lease_id, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_release_lease_request(
    url: str,
    *,
    lease_id: str,
    timeout: Optional[int] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["release"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "release"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_renew_lease_request(
    url: str,
    *,
    lease_id: str,
    timeout: Optional[int] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["renew"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "renew"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_change_lease_request(
    url: str,
    *,
    lease_id: str,
    proposed_lease_id: str,
    timeout: Optional[int] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["change"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "change"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-proposed-lease-id"] = _SERIALIZER.header("proposed_lease_id", proposed_lease_id, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_break_lease_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    break_period: Optional[int] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["break"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "break"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    if break_period is not None:
        _headers["x-ms-lease-break-period"] = _SERIALIZER.header("break_period", break_period, "int")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_create_snapshot_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    encryption_scope: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["snapshot"] = kwargs.pop("comp", _params.pop("comp", "snapshot"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_start_copy_from_url_request(
    url: str,
    *,
    copy_source: str,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    tier: Optional[Union[str, _models.AccessTierOptional]] = None,
    rehydrate_priority: Optional[Union[str, _models.RehydratePriority]] = None,
    source_if_modified_since: Optional[datetime.datetime] = None,
    source_if_unmodified_since: Optional[datetime.datetime] = None,
    source_if_match: Optional[str] = None,
    source_if_none_match: Optional[str] = None,
    source_if_tags: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    blob_tags_string: Optional[str] = None,
    seal_blob: Optional[bool] = None,
    immutability_policy_expiry: Optional[datetime.datetime] = None,
    immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
    legal_hold: Optional[bool] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if tier is not None:
        _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if rehydrate_priority is not None:
        _headers["x-ms-rehydrate-priority"] = _SERIALIZER.header("rehydrate_priority", rehydrate_priority, "str")
    if source_if_modified_since is not None:
        _headers["x-ms-source-if-modified-since"] = _SERIALIZER.header(
            "source_if_modified_since", source_if_modified_since, "rfc-1123"
        )
    if source_if_unmodified_since is not None:
        _headers["x-ms-source-if-unmodified-since"] = _SERIALIZER.header(
            "source_if_unmodified_since", source_if_unmodified_since, "rfc-1123"
        )
    if source_if_match is not None:
        _headers["x-ms-source-if-match"] = _SERIALIZER.header("source_if_match", source_if_match, "str")
    if source_if_none_match is not None:
        _headers["x-ms-source-if-none-match"] = _SERIALIZER.header("source_if_none_match", source_if_none_match, "str")
    if source_if_tags is not None:
        _headers["x-ms-source-if-tags"] = _SERIALIZER.header("source_if_tags", source_if_tags, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("copy_source", copy_source, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if blob_tags_string is not None:
        _headers["x-ms-tags"] = _SERIALIZER.header("blob_tags_string", blob_tags_string, "str")
    if seal_blob is not None:
        _headers["x-ms-seal-blob"] = _SERIALIZER.header("seal_blob", seal_blob, "bool")
    if immutability_policy_expiry is not None:
        _headers["x-ms-immutability-policy-until-date"] = _SERIALIZER.header(
            "immutability_policy_expiry", immutability_policy_expiry, "rfc-1123"
        )
    if immutability_policy_mode is not None:
        _headers["x-ms-immutability-policy-mode"] = _SERIALIZER.header(
            "immutability_policy_mode", immutability_policy_mode, "str"
        )
    if legal_hold is not None:
        _headers["x-ms-legal-hold"] = _SERIALIZER.header("legal_hold", legal_hold, "bool")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_copy_from_url_request(
    url: str,
    *,
    copy_source: str,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    tier: Optional[Union[str, _models.AccessTierOptional]] = None,
    source_if_modified_since: Optional[datetime.datetime] = None,
    source_if_unmodified_since: Optional[datetime.datetime] = None,
    source_if_match: Optional[str] = None,
    source_if_none_match: Optional[str] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    source_content_md5: Optional[bytes] = None,
    blob_tags_string: Optional[str] = None,
    immutability_policy_expiry: Optional[datetime.datetime] = None,
    immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
    legal_hold: Optional[bool] = None,
    copy_source_authorization: Optional[str] = None,
    encryption_scope: Optional[str] = None,
    copy_source_tags: Optional[Union[str, _models.BlobCopySourceTags]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    x_ms_requires_sync: Literal["true"] = kwargs.pop("x_ms_requires_sync", _headers.pop("x-ms-requires-sync", "true"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-requires-sync"] = _SERIALIZER.header("x_ms_requires_sync", x_ms_requires_sync, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if tier is not None:
        _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if source_if_modified_since is not None:
        _headers["x-ms-source-if-modified-since"] = _SERIALIZER.header(
            "source_if_modified_since", source_if_modified_since, "rfc-1123"
        )
    if source_if_unmodified_since is not None:
        _headers["x-ms-source-if-unmodified-since"] = _SERIALIZER.header(
            "source_if_unmodified_since", source_if_unmodified_since, "rfc-1123"
        )
    if source_if_match is not None:
        _headers["x-ms-source-if-match"] = _SERIALIZER.header("source_if_match", source_if_match, "str")
    if source_if_none_match is not None:
        _headers["x-ms-source-if-none-match"] = _SERIALIZER.header("source_if_none_match", source_if_none_match, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("copy_source", copy_source, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if source_content_md5 is not None:
        _headers["x-ms-source-content-md5"] = _SERIALIZER.header("source_content_md5", source_content_md5, "bytearray")
    if blob_tags_string is not None:
        _headers["x-ms-tags"] = _SERIALIZER.header("blob_tags_string", blob_tags_string, "str")
    if immutability_policy_expiry is not None:
        _headers["x-ms-immutability-policy-until-date"] = _SERIALIZER.header(
            "immutability_policy_expiry", immutability_policy_expiry, "rfc-1123"
        )
    if immutability_policy_mode is not None:
        _headers["x-ms-immutability-policy-mode"] = _SERIALIZER.header(
            "immutability_policy_mode", immutability_policy_mode, "str"
        )
    if legal_hold is not None:
        _headers["x-ms-legal-hold"] = _SERIALIZER.header("legal_hold", legal_hold, "bool")
    if copy_source_authorization is not None:
        _headers["x-ms-copy-source-authorization"] = _SERIALIZER.header(
            "copy_source_authorization", copy_source_authorization, "str"
        )
    if encryption_scope is not None:
        _headers["x-ms-encryption-scope"] = _SERIALIZER.header("encryption_scope", encryption_scope, "str")
    if copy_source_tags is not None:
        _headers["x-ms-copy-source-tag-option"] = _SERIALIZER.header("copy_source_tags", copy_source_tags, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_abort_copy_from_url_request(
    url: str,
    *,
    copy_id: str,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["copy"] = kwargs.pop("comp", _params.pop("comp", "copy"))
    copy_action_abort_constant: Literal["abort"] = kwargs.pop(
        "copy_action_abort_constant", _headers.pop("x-ms-copy-action", "abort")
    )
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    _params["copyid"] = _SERIALIZER.query("copy_id", copy_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-copy-action"] = _SERIALIZER.header("copy_action_abort_constant", copy_action_abort_constant, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_tier_request(
    url: str,
    *,
    tier: Union[str, _models.AccessTierRequired],
    snapshot: Optional[str] = None,
    version_id: Optional[str] = None,
    timeout: Optional[int] = None,
    rehydrate_priority: Optional[Union[str, _models.RehydratePriority]] = None,
    request_id_parameter: Optional[str] = None,
    lease_id: Optional[str] = None,
    if_tags: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["tier"] = kwargs.pop("comp", _params.pop("comp", "tier"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-access-tier"] = _SERIALIZER.header("tier", tier, "str")
    if rehydrate_priority is not None:
        _headers["x-ms-rehydrate-priority"] = _SERIALIZER.header("rehydrate_priority", rehydrate_priority, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_get_account_info_request(
    url: str, *, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    restype: Literal["account"] = kwargs.pop("restype", _params.pop("restype", "account"))
    comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["restype"] = _SERIALIZER.query("restype", restype, "str")
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_query_request(
    url: str,
    *,
    snapshot: Optional[str] = None,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    encryption_key: Optional[str] = None,
    encryption_key_sha256: Optional[str] = None,
    encryption_algorithm: Optional[Union[str, _models.EncryptionAlgorithmType]] = None,
    if_modified_since: Optional[datetime.datetime] = None,
    if_unmodified_since: Optional[datetime.datetime] = None,
    if_match: Optional[str] = None,
    if_none_match: Optional[str] = None,
    if_tags: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    content: Any = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["query"] = kwargs.pop("comp", _params.pop("comp", "query"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if encryption_key is not None:
        _headers["x-ms-encryption-key"] = _SERIALIZER.header("encryption_key", encryption_key, "str")
    if encryption_key_sha256 is not None:
        _headers["x-ms-encryption-key-sha256"] = _SERIALIZER.header(
            "encryption_key_sha256", encryption_key_sha256, "str"
        )
    if encryption_algorithm is not None:
        _headers["x-ms-encryption-algorithm"] = _SERIALIZER.header("encryption_algorithm", encryption_algorithm, "str")
    if if_modified_since is not None:
        _headers["If-Modified-Since"] = _SERIALIZER.header("if_modified_since", if_modified_since, "rfc-1123")
    if if_unmodified_since is not None:
        _headers["If-Unmodified-Since"] = _SERIALIZER.header("if_unmodified_since", if_unmodified_since, "rfc-1123")
    if if_match is not None:
        _headers["If-Match"] = _SERIALIZER.header("if_match", if_match, "str")
    if if_none_match is not None:
        _headers["If-None-Match"] = _SERIALIZER.header("if_none_match", if_none_match, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, content=content, **kwargs)


def build_get_tags_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    snapshot: Optional[str] = None,
    version_id: Optional[str] = None,
    if_tags: Optional[str] = None,
    lease_id: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["tags"] = kwargs.pop("comp", _params.pop("comp", "tags"))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if snapshot is not None:
        _params["snapshot"] = _SERIALIZER.query("snapshot", snapshot, "str")
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_tags_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    version_id: Optional[str] = None,
    transactional_content_md5: Optional[bytes] = None,
    transactional_content_crc64: Optional[bytes] = None,
    request_id_parameter: Optional[str] = None,
    if_tags: Optional[str] = None,
    lease_id: Optional[str] = None,
    content: Any = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["tags"] = kwargs.pop("comp", _params.pop("comp", "tags"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2024-08-04"] = kwargs.pop("version", _headers.pop("x-ms-version", "2024-08-04"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if version_id is not None:
        _params["versionid"] = _SERIALIZER.query("version_id", version_id, "str")

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if transactional_content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header(
            "transactional_content_md5", transactional_content_md5, "bytearray"
        )
    if transactional_content_crc64 is not None:
        _headers["x-ms-content-crc64"] = _SERIALIZER.header(
            "transactional_content_crc64", transactional_content_crc64, "bytearray"
        )
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if if_tags is not None:
        _headers["x-ms-if-tags"] = _SERIALIZER.header("if_tags", if_tags, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, content=content, **kwargs)


class BlobOperations:  # pylint: disable=too-many-public-methods
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.storage.blob.AzureBlobStorage`'s
        :attr:`blob` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs):
        input_args = list(args)
        self._client = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @distributed_trace
    def download(
        self,
        snapshot: Optional[str] = None,
        version_id: Optional[str] = None,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        range_get_content_md5: Optional[bool] = None,
        range_get_content_crc64: Optional[bool] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> Iterator[bytes]:
        """The Download operation reads or downloads a blob from the system, including its metadata and
        properties. You can also call Download to read a snapshot.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Return only the bytes of the blob in the specified range. Default value is None.
        :type range: str
        :param range_get_content_md5: When set to true and specified together with the Range, the
         service returns the MD5 hash for the range, as long as the range is less than or equal to 4 MB
         in size. Default value is None.
        :type range_get_content_md5: bool
        :param range_get_content_crc64: When set to true and specified together with the Range, the
         service returns the CRC64 hash for the range, as long as the range is less than or equal to 4
         MB in size. Default value is None.
        :type range_get_content_crc64: bool
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: Iterator[bytes] or the result of cls(response)
        :rtype: Iterator[bytes]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[Iterator[bytes]] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_download_request(
            url=self._config.url,
            snapshot=snapshot,
            version_id=version_id,
            timeout=timeout,
            range=range,
            lease_id=_lease_id,
            range_get_content_md5=range_get_content_md5,
            range_get_content_crc64=range_get_content_crc64,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = True
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 206]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        if response.status_code == 200:
            response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
            response_headers["x-ms-creation-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-creation-time")
            )
            response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
            response_headers["x-ms-or-policy-id"] = self._deserialize("str", response.headers.get("x-ms-or-policy-id"))
            response_headers["x-ms-or"] = self._deserialize("{str}", response.headers.get("x-ms-or"))
            response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
            response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
            response_headers["Content-Range"] = self._deserialize("str", response.headers.get("Content-Range"))
            response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
            response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
            response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
            response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
            response_headers["Content-Disposition"] = self._deserialize(
                "str", response.headers.get("Content-Disposition")
            )
            response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
            response_headers["x-ms-blob-sequence-number"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-sequence-number")
            )
            response_headers["x-ms-blob-type"] = self._deserialize("str", response.headers.get("x-ms-blob-type"))
            response_headers["x-ms-copy-completion-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-copy-completion-time")
            )
            response_headers["x-ms-copy-status-description"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-status-description")
            )
            response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
            response_headers["x-ms-copy-progress"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-progress")
            )
            response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
            response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
            response_headers["x-ms-lease-duration"] = self._deserialize(
                "str", response.headers.get("x-ms-lease-duration")
            )
            response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
            response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
            response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
            response_headers["x-ms-is-current-version"] = self._deserialize(
                "bool", response.headers.get("x-ms-is-current-version")
            )
            response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
            response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
            response_headers["x-ms-blob-committed-block-count"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-committed-block-count")
            )
            response_headers["x-ms-server-encrypted"] = self._deserialize(
                "bool", response.headers.get("x-ms-server-encrypted")
            )
            response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-key-sha256")
            )
            response_headers["x-ms-encryption-scope"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-scope")
            )
            response_headers["x-ms-blob-content-md5"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-blob-content-md5")
            )
            response_headers["x-ms-tag-count"] = self._deserialize("int", response.headers.get("x-ms-tag-count"))
            response_headers["x-ms-blob-sealed"] = self._deserialize("bool", response.headers.get("x-ms-blob-sealed"))
            response_headers["x-ms-last-access-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-last-access-time")
            )
            response_headers["x-ms-immutability-policy-until-date"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-immutability-policy-until-date")
            )
            response_headers["x-ms-immutability-policy-mode"] = self._deserialize(
                "str", response.headers.get("x-ms-immutability-policy-mode")
            )
            response_headers["x-ms-legal-hold"] = self._deserialize("bool", response.headers.get("x-ms-legal-hold"))

            deserialized = response.stream_download(self._client._pipeline)

        if response.status_code == 206:
            response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
            response_headers["x-ms-creation-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-creation-time")
            )
            response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
            response_headers["x-ms-or-policy-id"] = self._deserialize("str", response.headers.get("x-ms-or-policy-id"))
            response_headers["x-ms-or"] = self._deserialize("{str}", response.headers.get("x-ms-or"))
            response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
            response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
            response_headers["Content-Range"] = self._deserialize("str", response.headers.get("Content-Range"))
            response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
            response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
            response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
            response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
            response_headers["Content-Disposition"] = self._deserialize(
                "str", response.headers.get("Content-Disposition")
            )
            response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
            response_headers["x-ms-blob-sequence-number"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-sequence-number")
            )
            response_headers["x-ms-blob-type"] = self._deserialize("str", response.headers.get("x-ms-blob-type"))
            response_headers["x-ms-content-crc64"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-content-crc64")
            )
            response_headers["x-ms-copy-completion-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-copy-completion-time")
            )
            response_headers["x-ms-copy-status-description"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-status-description")
            )
            response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
            response_headers["x-ms-copy-progress"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-progress")
            )
            response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
            response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
            response_headers["x-ms-lease-duration"] = self._deserialize(
                "str", response.headers.get("x-ms-lease-duration")
            )
            response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
            response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
            response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
            response_headers["x-ms-is-current-version"] = self._deserialize(
                "bool", response.headers.get("x-ms-is-current-version")
            )
            response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
            response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
            response_headers["x-ms-blob-committed-block-count"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-committed-block-count")
            )
            response_headers["x-ms-server-encrypted"] = self._deserialize(
                "bool", response.headers.get("x-ms-server-encrypted")
            )
            response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-key-sha256")
            )
            response_headers["x-ms-encryption-scope"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-scope")
            )
            response_headers["x-ms-blob-content-md5"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-blob-content-md5")
            )
            response_headers["x-ms-tag-count"] = self._deserialize("int", response.headers.get("x-ms-tag-count"))
            response_headers["x-ms-blob-sealed"] = self._deserialize("bool", response.headers.get("x-ms-blob-sealed"))
            response_headers["x-ms-last-access-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-last-access-time")
            )
            response_headers["x-ms-immutability-policy-until-date"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-immutability-policy-until-date")
            )
            response_headers["x-ms-immutability-policy-mode"] = self._deserialize(
                "str", response.headers.get("x-ms-immutability-policy-mode")
            )
            response_headers["x-ms-legal-hold"] = self._deserialize("bool", response.headers.get("x-ms-legal-hold"))

            deserialized = response.stream_download(self._client._pipeline)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_properties(  # pylint: disable=inconsistent-return-statements
        self,
        snapshot: Optional[str] = None,
        version_id: Optional[str] = None,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Get Properties operation returns all user-defined metadata, standard HTTP properties, and
        system properties for the blob. It does not return the content of the blob.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_get_properties_request(
            url=self._config.url,
            snapshot=snapshot,
            version_id=version_id,
            timeout=timeout,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-creation-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-creation-time")
        )
        response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
        response_headers["x-ms-or-policy-id"] = self._deserialize("str", response.headers.get("x-ms-or-policy-id"))
        response_headers["x-ms-or"] = self._deserialize("{str}", response.headers.get("x-ms-or"))
        response_headers["x-ms-blob-type"] = self._deserialize("str", response.headers.get("x-ms-blob-type"))
        response_headers["x-ms-copy-completion-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-copy-completion-time")
        )
        response_headers["x-ms-copy-status-description"] = self._deserialize(
            "str", response.headers.get("x-ms-copy-status-description")
        )
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-progress"] = self._deserialize("str", response.headers.get("x-ms-copy-progress"))
        response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
        response_headers["x-ms-incremental-copy"] = self._deserialize(
            "bool", response.headers.get("x-ms-incremental-copy")
        )
        response_headers["x-ms-copy-destination-snapshot"] = self._deserialize(
            "str", response.headers.get("x-ms-copy-destination-snapshot")
        )
        response_headers["x-ms-lease-duration"] = self._deserialize("str", response.headers.get("x-ms-lease-duration"))
        response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
        response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
        response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
        response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
        response_headers["Content-Disposition"] = self._deserialize("str", response.headers.get("Content-Disposition"))
        response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
        response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
        response_headers["x-ms-blob-committed-block-count"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-committed-block-count")
        )
        response_headers["x-ms-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )
        response_headers["x-ms-access-tier"] = self._deserialize("str", response.headers.get("x-ms-access-tier"))
        response_headers["x-ms-access-tier-inferred"] = self._deserialize(
            "bool", response.headers.get("x-ms-access-tier-inferred")
        )
        response_headers["x-ms-archive-status"] = self._deserialize("str", response.headers.get("x-ms-archive-status"))
        response_headers["x-ms-access-tier-change-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-access-tier-change-time")
        )
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["x-ms-is-current-version"] = self._deserialize(
            "bool", response.headers.get("x-ms-is-current-version")
        )
        response_headers["x-ms-tag-count"] = self._deserialize("int", response.headers.get("x-ms-tag-count"))
        response_headers["x-ms-expiry-time"] = self._deserialize("rfc-1123", response.headers.get("x-ms-expiry-time"))
        response_headers["x-ms-blob-sealed"] = self._deserialize("bool", response.headers.get("x-ms-blob-sealed"))
        response_headers["x-ms-rehydrate-priority"] = self._deserialize(
            "str", response.headers.get("x-ms-rehydrate-priority")
        )
        response_headers["x-ms-last-access-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-last-access-time")
        )
        response_headers["x-ms-immutability-policy-until-date"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-immutability-policy-until-date")
        )
        response_headers["x-ms-immutability-policy-mode"] = self._deserialize(
            "str", response.headers.get("x-ms-immutability-policy-mode")
        )
        response_headers["x-ms-legal-hold"] = self._deserialize("bool", response.headers.get("x-ms-legal-hold"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def delete(  # pylint: disable=inconsistent-return-statements
        self,
        snapshot: Optional[str] = None,
        version_id: Optional[str] = None,
        timeout: Optional[int] = None,
        delete_snapshots: Optional[Union[str, _models.DeleteSnapshotsOptionType]] = None,
        request_id_parameter: Optional[str] = None,
        blob_delete_type: Literal["Permanent"] = "Permanent",
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """If the storage account's soft delete feature is disabled then, when a blob is deleted, it is
        permanently removed from the storage account. If the storage account's soft delete feature is
        enabled, then, when a blob is deleted, it is marked for deletion and becomes inaccessible
        immediately. However, the blob service retains the blob or snapshot for the number of days
        specified by the DeleteRetentionPolicy section of [Storage service properties]
        (Set-Blob-Service-Properties.md). After the specified number of days has passed, the blob's
        data is permanently removed from the storage account. Note that you continue to be charged for
        the soft-deleted blob's storage until it is permanently removed. Use the List Blobs API and
        specify the "include=deleted" query parameter to discover which blobs and snapshots have been
        soft deleted. You can then use the Undelete Blob API to restore a soft-deleted blob. All other
        operations on a soft-deleted blob or snapshot causes the service to return an HTTP status code
        of 404 (ResourceNotFound).

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param delete_snapshots: Required if the blob has associated snapshots. Specify one of the
         following two options: include: Delete the base blob and all of its snapshots. only: Delete
         only the blob's snapshots and not the blob itself. Known values are: "include" and "only".
         Default value is None.
        :type delete_snapshots: str or ~azure.storage.blob.models.DeleteSnapshotsOptionType
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_delete_type: Optional.  Only possible value is 'permanent', which specifies to
         permanently delete a blob if blob soft delete is enabled. Known values are "Permanent" and
         None. Default value is "Permanent".
        :type blob_delete_type: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_delete_request(
            url=self._config.url,
            snapshot=snapshot,
            version_id=version_id,
            timeout=timeout,
            lease_id=_lease_id,
            delete_snapshots=delete_snapshots,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            blob_delete_type=blob_delete_type,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def undelete(  # pylint: disable=inconsistent-return-statements
        self, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """Undelete a blob that was previously soft deleted.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["undelete"] = kwargs.pop("comp", _params.pop("comp", "undelete"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_undelete_request(
            url=self._config.url,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_expiry(  # pylint: disable=inconsistent-return-statements
        self,
        expiry_options: Union[str, _models.BlobExpiryOptions],
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        expires_on: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """Sets the time a blob will expire and be deleted.

        :param expiry_options: Required. Indicates mode of the expiry time. Known values are:
         "NeverExpire", "RelativeToCreation", "RelativeToNow", and "Absolute". Required.
        :type expiry_options: str or ~azure.storage.blob.models.BlobExpiryOptions
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param expires_on: The time to set the blob to expiry. Default value is None.
        :type expires_on: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["expiry"] = kwargs.pop("comp", _params.pop("comp", "expiry"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_set_expiry_request(
            url=self._config.url,
            expiry_options=expiry_options,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            expires_on=expires_on,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_http_headers(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        blob_http_headers: Optional[_models.BlobHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Set HTTP Headers operation sets system properties on the blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_http_headers: Parameter group. Default value is None.
        :type blob_http_headers: ~azure.storage.blob.models.BlobHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _blob_cache_control = None
        _blob_content_type = None
        _blob_content_md5 = None
        _blob_content_encoding = None
        _blob_content_language = None
        _lease_id = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _blob_content_disposition = None
        if blob_http_headers is not None:
            _blob_cache_control = blob_http_headers.blob_cache_control
            _blob_content_disposition = blob_http_headers.blob_content_disposition
            _blob_content_encoding = blob_http_headers.blob_content_encoding
            _blob_content_language = blob_http_headers.blob_content_language
            _blob_content_md5 = blob_http_headers.blob_content_md5
            _blob_content_type = blob_http_headers.blob_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_set_http_headers_request(
            url=self._config.url,
            timeout=timeout,
            blob_cache_control=_blob_cache_control,
            blob_content_type=_blob_content_type,
            blob_content_md5=_blob_content_md5,
            blob_content_encoding=_blob_content_encoding,
            blob_content_language=_blob_content_language,
            lease_id=_lease_id,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            blob_content_disposition=_blob_content_disposition,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_immutability_policy(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Set Immutability Policy operation sets the immutability policy on the blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["immutabilityPolicies"] = kwargs.pop("comp", _params.pop("comp", "immutabilityPolicies"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_unmodified_since = None
        if modified_access_conditions is not None:
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_set_immutability_policy_request(
            url=self._config.url,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            if_unmodified_since=_if_unmodified_since,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-immutability-policy-until-date"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-immutability-policy-until-date")
        )
        response_headers["x-ms-immutability-policy-mode"] = self._deserialize(
            "str", response.headers.get("x-ms-immutability-policy-mode")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def delete_immutability_policy(  # pylint: disable=inconsistent-return-statements
        self, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """The Delete Immutability Policy operation deletes the immutability policy on the blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["immutabilityPolicies"] = kwargs.pop("comp", _params.pop("comp", "immutabilityPolicies"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_delete_immutability_policy_request(
            url=self._config.url,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_legal_hold(  # pylint: disable=inconsistent-return-statements
        self, legal_hold: bool, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """The Set Legal Hold operation sets a legal hold on the blob.

        :param legal_hold: Specified if a legal hold should be set on the blob. Required.
        :type legal_hold: bool
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["legalhold"] = kwargs.pop("comp", _params.pop("comp", "legalhold"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_set_legal_hold_request(
            url=self._config.url,
            legal_hold=legal_hold,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-legal-hold"] = self._deserialize("bool", response.headers.get("x-ms-legal-hold"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_metadata(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Set Blob Metadata operation sets user-defined metadata for the specified blob as one or
        more name-value pairs.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["metadata"] = kwargs.pop("comp", _params.pop("comp", "metadata"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_set_metadata_request(
            url=self._config.url,
            timeout=timeout,
            metadata=metadata,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def acquire_lease(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        duration: Optional[int] = None,
        proposed_lease_id: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete
        operations.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param duration: Specifies the duration of the lease, in seconds, or negative one (-1) for a
         lease that never expires. A non-infinite lease can be between 15 and 60 seconds. A lease
         duration cannot be changed using renew or change. Default value is None.
        :type duration: int
        :param proposed_lease_id: Proposed lease ID, in a GUID string format. The Blob service returns
         400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid
         Constructor (String) for a list of valid GUID string formats. Default value is None.
        :type proposed_lease_id: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["acquire"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "acquire"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_acquire_lease_request(
            url=self._config.url,
            timeout=timeout,
            duration=duration,
            proposed_lease_id=proposed_lease_id,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def release_lease(  # pylint: disable=inconsistent-return-statements
        self,
        lease_id: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete
        operations.

        :param lease_id: Specifies the current lease ID on the resource. Required.
        :type lease_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["release"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "release"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_release_lease_request(
            url=self._config.url,
            lease_id=lease_id,
            timeout=timeout,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def renew_lease(  # pylint: disable=inconsistent-return-statements
        self,
        lease_id: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete
        operations.

        :param lease_id: Specifies the current lease ID on the resource. Required.
        :type lease_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["renew"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "renew"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_renew_lease_request(
            url=self._config.url,
            lease_id=lease_id,
            timeout=timeout,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def change_lease(  # pylint: disable=inconsistent-return-statements
        self,
        lease_id: str,
        proposed_lease_id: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete
        operations.

        :param lease_id: Specifies the current lease ID on the resource. Required.
        :type lease_id: str
        :param proposed_lease_id: Proposed lease ID, in a GUID string format. The Blob service returns
         400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid
         Constructor (String) for a list of valid GUID string formats. Required.
        :type proposed_lease_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["change"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "change"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_change_lease_request(
            url=self._config.url,
            lease_id=lease_id,
            proposed_lease_id=proposed_lease_id,
            timeout=timeout,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def break_lease(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        break_period: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete
        operations.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param break_period: For a break operation, proposed duration the lease should continue before
         it is broken, in seconds, between 0 and 60. This break period is only used if it is shorter
         than the time remaining on the lease. If longer, the time remaining on the lease is used. A new
         lease will not be available before the break period has expired, but the lease may be held for
         longer than the break period. If this header does not appear with a break operation, a
         fixed-duration lease breaks after the remaining lease period elapses, and an infinite lease
         breaks immediately. Default value is None.
        :type break_period: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["break"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "break"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_break_lease_request(
            url=self._config.url,
            timeout=timeout,
            break_period=break_period,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-time"] = self._deserialize("int", response.headers.get("x-ms-lease-time"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def create_snapshot(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        request_id_parameter: Optional[str] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Create Snapshot operation creates a read-only snapshot of a blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["snapshot"] = kwargs.pop("comp", _params.pop("comp", "snapshot"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _lease_id = None
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_create_snapshot_request(
            url=self._config.url,
            timeout=timeout,
            metadata=metadata,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            lease_id=_lease_id,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-snapshot"] = self._deserialize("str", response.headers.get("x-ms-snapshot"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def start_copy_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        copy_source: str,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        tier: Optional[Union[str, _models.AccessTierOptional]] = None,
        rehydrate_priority: Optional[Union[str, _models.RehydratePriority]] = None,
        request_id_parameter: Optional[str] = None,
        blob_tags_string: Optional[str] = None,
        seal_blob: Optional[bool] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        legal_hold: Optional[bool] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Start Copy From URL operation copies a blob or an internet resource to a new blob.

        :param copy_source: Specifies the name of the source page blob snapshot. This value is a URL of
         up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it
         would appear in a request URI. The source blob must either be public or must be authenticated
         via a shared access signature. Required.
        :type copy_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param tier: Optional. Indicates the tier to be set on the blob. Known values are: "P4", "P6",
         "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and
         "Cold". Default value is None.
        :type tier: str or ~azure.storage.blob.models.AccessTierOptional
        :param rehydrate_priority: Optional: Indicates the priority with which to rehydrate an archived
         blob. Known values are: "High" and "Standard". Default value is None.
        :type rehydrate_priority: str or ~azure.storage.blob.models.RehydratePriority
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param seal_blob: Overrides the sealed state of the destination blob.  Service version
         2019-12-12 and newer. Default value is None.
        :type seal_blob: bool
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param legal_hold: Specified if a legal hold should be set on the blob. Default value is None.
        :type legal_hold: bool
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.blob.models.SourceModifiedAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _source_if_modified_since = None
        _source_if_unmodified_since = None
        _source_if_match = None
        _source_if_none_match = None
        _source_if_tags = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _lease_id = None
        if source_modified_access_conditions is not None:
            _source_if_match = source_modified_access_conditions.source_if_match
            _source_if_modified_since = source_modified_access_conditions.source_if_modified_since
            _source_if_none_match = source_modified_access_conditions.source_if_none_match
            _source_if_tags = source_modified_access_conditions.source_if_tags
            _source_if_unmodified_since = source_modified_access_conditions.source_if_unmodified_since
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_start_copy_from_url_request(
            url=self._config.url,
            copy_source=copy_source,
            timeout=timeout,
            metadata=metadata,
            tier=tier,
            rehydrate_priority=rehydrate_priority,
            source_if_modified_since=_source_if_modified_since,
            source_if_unmodified_since=_source_if_unmodified_since,
            source_if_match=_source_if_match,
            source_if_none_match=_source_if_none_match,
            source_if_tags=_source_if_tags,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            lease_id=_lease_id,
            request_id_parameter=request_id_parameter,
            blob_tags_string=blob_tags_string,
            seal_blob=seal_blob,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            legal_hold=legal_hold,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def copy_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        copy_source: str,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        tier: Optional[Union[str, _models.AccessTierOptional]] = None,
        request_id_parameter: Optional[str] = None,
        source_content_md5: Optional[bytes] = None,
        blob_tags_string: Optional[str] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        legal_hold: Optional[bool] = None,
        copy_source_authorization: Optional[str] = None,
        copy_source_tags: Optional[Union[str, _models.BlobCopySourceTags]] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        **kwargs: Any
    ) -> None:
        """The Copy From URL operation copies a blob or an internet resource to a new blob. It will not
        return a response until the copy is complete.

        :param copy_source: Specifies the name of the source page blob snapshot. This value is a URL of
         up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it
         would appear in a request URI. The source blob must either be public or must be authenticated
         via a shared access signature. Required.
        :type copy_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param tier: Optional. Indicates the tier to be set on the blob. Known values are: "P4", "P6",
         "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and
         "Cold". Default value is None.
        :type tier: str or ~azure.storage.blob.models.AccessTierOptional
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param source_content_md5: Specify the md5 calculated for the range of bytes that must be read
         from the copy source. Default value is None.
        :type source_content_md5: bytes
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param legal_hold: Specified if a legal hold should be set on the blob. Default value is None.
        :type legal_hold: bool
        :param copy_source_authorization: Only Bearer type is supported. Credentials should be a valid
         OAuth access token to copy source. Default value is None.
        :type copy_source_authorization: str
        :param copy_source_tags: Optional, default 'replace'.  Indicates if source tags should be
         copied or replaced with the tags specified by x-ms-tags. Known values are: "REPLACE" and
         "COPY". Default value is None.
        :type copy_source_tags: str or ~azure.storage.blob.models.BlobCopySourceTags
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.blob.models.SourceModifiedAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        x_ms_requires_sync: Literal["true"] = kwargs.pop(
            "x_ms_requires_sync", _headers.pop("x-ms-requires-sync", "true")
        )
        cls: ClsType[None] = kwargs.pop("cls", None)

        _source_if_modified_since = None
        _source_if_unmodified_since = None
        _source_if_match = None
        _source_if_none_match = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _lease_id = None
        _encryption_scope = None
        if source_modified_access_conditions is not None:
            _source_if_match = source_modified_access_conditions.source_if_match
            _source_if_modified_since = source_modified_access_conditions.source_if_modified_since
            _source_if_none_match = source_modified_access_conditions.source_if_none_match
            _source_if_unmodified_since = source_modified_access_conditions.source_if_unmodified_since
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope

        _request = build_copy_from_url_request(
            url=self._config.url,
            copy_source=copy_source,
            timeout=timeout,
            metadata=metadata,
            tier=tier,
            source_if_modified_since=_source_if_modified_since,
            source_if_unmodified_since=_source_if_unmodified_since,
            source_if_match=_source_if_match,
            source_if_none_match=_source_if_none_match,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            lease_id=_lease_id,
            request_id_parameter=request_id_parameter,
            source_content_md5=source_content_md5,
            blob_tags_string=blob_tags_string,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            legal_hold=legal_hold,
            copy_source_authorization=copy_source_authorization,
            encryption_scope=_encryption_scope,
            copy_source_tags=copy_source_tags,
            x_ms_requires_sync=x_ms_requires_sync,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def abort_copy_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        copy_id: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Abort Copy From URL operation aborts a pending Copy From URL operation, and leaves a
        destination blob with zero length and full metadata.

        :param copy_id: The copy identifier provided in the x-ms-copy-id header of the original Copy
         Blob operation. Required.
        :type copy_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["copy"] = kwargs.pop("comp", _params.pop("comp", "copy"))
        copy_action_abort_constant: Literal["abort"] = kwargs.pop(
            "copy_action_abort_constant", _headers.pop("x-ms-copy-action", "abort")
        )
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_abort_copy_from_url_request(
            url=self._config.url,
            copy_id=copy_id,
            timeout=timeout,
            lease_id=_lease_id,
            request_id_parameter=request_id_parameter,
            comp=comp,
            copy_action_abort_constant=copy_action_abort_constant,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_tier(  # pylint: disable=inconsistent-return-statements
        self,
        tier: Union[str, _models.AccessTierRequired],
        snapshot: Optional[str] = None,
        version_id: Optional[str] = None,
        timeout: Optional[int] = None,
        rehydrate_priority: Optional[Union[str, _models.RehydratePriority]] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """The Set Tier operation sets the tier on a blob. The operation is allowed on a page blob in a
        premium storage account and on a block blob in a blob storage account (locally redundant
        storage only). A premium page blob's tier determines the allowed size, IOPS, and bandwidth of
        the blob. A block blob's tier determines Hot/Cool/Archive storage type. This operation does not
        update the blob's ETag.

        :param tier: Indicates the tier to be set on the blob. Known values are: "P4", "P6", "P10",
         "P15", "P20", "P30", "P40", "P50", "P60", "P70", "P80", "Hot", "Cool", "Archive", and "Cold".
         Required.
        :type tier: str or ~azure.storage.blob.models.AccessTierRequired
        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param rehydrate_priority: Optional: Indicates the priority with which to rehydrate an archived
         blob. Known values are: "High" and "Standard". Default value is None.
        :type rehydrate_priority: str or ~azure.storage.blob.models.RehydratePriority
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["tier"] = kwargs.pop("comp", _params.pop("comp", "tier"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_tags = modified_access_conditions.if_tags

        _request = build_set_tier_request(
            url=self._config.url,
            tier=tier,
            snapshot=snapshot,
            version_id=version_id,
            timeout=timeout,
            rehydrate_priority=rehydrate_priority,
            request_id_parameter=request_id_parameter,
            lease_id=_lease_id,
            if_tags=_if_tags,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        if response.status_code == 200:
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))

        if response.status_code == 202:
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def get_account_info(  # pylint: disable=inconsistent-return-statements
        self, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """Returns the sku name and account kind.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        restype: Literal["account"] = kwargs.pop("restype", _params.pop("restype", "account"))
        comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_get_account_info_request(
            url=self._config.url,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            restype=restype,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-sku-name"] = self._deserialize("str", response.headers.get("x-ms-sku-name"))
        response_headers["x-ms-account-kind"] = self._deserialize("str", response.headers.get("x-ms-account-kind"))
        response_headers["x-ms-is-hns-enabled"] = self._deserialize("bool", response.headers.get("x-ms-is-hns-enabled"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def query(
        self,
        snapshot: Optional[str] = None,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        query_request: Optional[_models.QueryRequest] = None,
        **kwargs: Any
    ) -> Iterator[bytes]:
        """The Query operation enables users to select/project on blob data by providing simple query
        expressions.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param query_request: the query request. Default value is None.
        :type query_request: ~azure.storage.blob.models.QueryRequest
        :return: Iterator[bytes] or the result of cls(response)
        :rtype: Iterator[bytes]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["query"] = kwargs.pop("comp", _params.pop("comp", "query"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/xml"))
        cls: ClsType[Iterator[bytes]] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if query_request is not None:
            _content = self._serialize.body(query_request, "QueryRequest", is_xml=True)
        else:
            _content = None

        _request = build_query_request(
            url=self._config.url,
            snapshot=snapshot,
            timeout=timeout,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = True
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 206]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        if response.status_code == 200:
            response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
            response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
            response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
            response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
            response_headers["Content-Range"] = self._deserialize("str", response.headers.get("Content-Range"))
            response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
            response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
            response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
            response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
            response_headers["Content-Disposition"] = self._deserialize(
                "str", response.headers.get("Content-Disposition")
            )
            response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
            response_headers["x-ms-blob-sequence-number"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-sequence-number")
            )
            response_headers["x-ms-blob-type"] = self._deserialize("str", response.headers.get("x-ms-blob-type"))
            response_headers["x-ms-copy-completion-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-copy-completion-time")
            )
            response_headers["x-ms-copy-status-description"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-status-description")
            )
            response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
            response_headers["x-ms-copy-progress"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-progress")
            )
            response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
            response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
            response_headers["x-ms-lease-duration"] = self._deserialize(
                "str", response.headers.get("x-ms-lease-duration")
            )
            response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
            response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
            response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
            response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
            response_headers["x-ms-blob-committed-block-count"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-committed-block-count")
            )
            response_headers["x-ms-server-encrypted"] = self._deserialize(
                "bool", response.headers.get("x-ms-server-encrypted")
            )
            response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-key-sha256")
            )
            response_headers["x-ms-encryption-scope"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-scope")
            )
            response_headers["x-ms-blob-content-md5"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-blob-content-md5")
            )

            deserialized = response.stream_download(self._client._pipeline)

        if response.status_code == 206:
            response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
            response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
            response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
            response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
            response_headers["Content-Range"] = self._deserialize("str", response.headers.get("Content-Range"))
            response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
            response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
            response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
            response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
            response_headers["Content-Disposition"] = self._deserialize(
                "str", response.headers.get("Content-Disposition")
            )
            response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
            response_headers["x-ms-blob-sequence-number"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-sequence-number")
            )
            response_headers["x-ms-blob-type"] = self._deserialize("str", response.headers.get("x-ms-blob-type"))
            response_headers["x-ms-content-crc64"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-content-crc64")
            )
            response_headers["x-ms-copy-completion-time"] = self._deserialize(
                "rfc-1123", response.headers.get("x-ms-copy-completion-time")
            )
            response_headers["x-ms-copy-status-description"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-status-description")
            )
            response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
            response_headers["x-ms-copy-progress"] = self._deserialize(
                "str", response.headers.get("x-ms-copy-progress")
            )
            response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
            response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
            response_headers["x-ms-lease-duration"] = self._deserialize(
                "str", response.headers.get("x-ms-lease-duration")
            )
            response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
            response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
            response_headers["x-ms-client-request-id"] = self._deserialize(
                "str", response.headers.get("x-ms-client-request-id")
            )
            response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
            response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
            response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
            response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
            response_headers["x-ms-blob-committed-block-count"] = self._deserialize(
                "int", response.headers.get("x-ms-blob-committed-block-count")
            )
            response_headers["x-ms-server-encrypted"] = self._deserialize(
                "bool", response.headers.get("x-ms-server-encrypted")
            )
            response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-key-sha256")
            )
            response_headers["x-ms-encryption-scope"] = self._deserialize(
                "str", response.headers.get("x-ms-encryption-scope")
            )
            response_headers["x-ms-blob-content-md5"] = self._deserialize(
                "bytearray", response.headers.get("x-ms-blob-content-md5")
            )

            deserialized = response.stream_download(self._client._pipeline)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_tags(
        self,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        snapshot: Optional[str] = None,
        version_id: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> _models.BlobTags:
        """The Get Tags operation enables users to get the tags associated with a blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :return: BlobTags or the result of cls(response)
        :rtype: ~azure.storage.blob.models.BlobTags
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["tags"] = kwargs.pop("comp", _params.pop("comp", "tags"))
        cls: ClsType[_models.BlobTags] = kwargs.pop("cls", None)

        _if_tags = None
        _lease_id = None
        if modified_access_conditions is not None:
            _if_tags = modified_access_conditions.if_tags
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_get_tags_request(
            url=self._config.url,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            snapshot=snapshot,
            version_id=version_id,
            if_tags=_if_tags,
            lease_id=_lease_id,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("BlobTags", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def set_tags(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        version_id: Optional[str] = None,
        transactional_content_md5: Optional[bytes] = None,
        transactional_content_crc64: Optional[bytes] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        tags: Optional[_models.BlobTags] = None,
        **kwargs: Any
    ) -> None:
        """The Set Tags operation enables users to set tags on a blob.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param version_id: The version id parameter is an opaque DateTime value that, when present,
         specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer.
         Default value is None.
        :type version_id: str
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param transactional_content_crc64: Specify the transactional crc64 for the body, to be
         validated by the service. Default value is None.
        :type transactional_content_crc64: bytes
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param tags: Blob tags. Default value is None.
        :type tags: ~azure.storage.blob.models.BlobTags
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["tags"] = kwargs.pop("comp", _params.pop("comp", "tags"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/xml"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_tags = None
        _lease_id = None
        if modified_access_conditions is not None:
            _if_tags = modified_access_conditions.if_tags
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if tags is not None:
            _content = self._serialize.body(tags, "BlobTags", is_xml=True)
        else:
            _content = None

        _request = build_set_tags_request(
            url=self._config.url,
            timeout=timeout,
            version_id=version_id,
            transactional_content_md5=transactional_content_md5,
            transactional_content_crc64=transactional_content_crc64,
            request_id_parameter=request_id_parameter,
            if_tags=_if_tags,
            lease_id=_lease_id,
            comp=comp,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request = _convert_request(_request)
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore
