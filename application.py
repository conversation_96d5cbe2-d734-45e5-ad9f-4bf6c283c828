import asyncio
import uuid
import threading
from datetime import datetime
from importlib.resources import Resource

from flask import Flask, request, jsonify
from google.cloud import vision
from google.oauth2 import service_account
from azure.storage.blob import BlobServiceClient
import google.generativeai as genai
import os
from flask_restful import Resource, Api
from flask_cors import CORS
import pyodbc  # or your preferred package for SQL Server
import json
import logging
from flask_restful import Api,  Resource as FlaskResource
from rapidfuzz import fuzz
from PIL import Image
from io import BytesIO
import requests


# Configure logging
logging.basicConfig(level=logging.INFO)
# Initialize Flask application
app = Flask(__name__)
CORS(app, origins=['http://localhost:4200','https://app.diginotice.in','https://diginotice.in' , 'https://staging.diginotice.in'])
api = Api(app)

# Set maximum upload size to 100MB (adjust as needed)
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024

# Set the timeout for requests to zero (unlimited)
@app.before_request
def set_request_timeout():
    request.environ['werkzeug.server.shutdown'] = lambda: None

# Google Cloud credentials setup
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = "diginotice-007-ef701c25e261.json"
credentials = service_account.Credentials.from_service_account_file(
    'diginotice-007-ef701c25e261.json')

# Initialize the Google Vision client with the credentials
vision_client = vision.ImageAnnotatorClient(credentials=credentials)

# Configure Generative AI with your API key
genai.configure(api_key="AIzaSyAjm_-azXAryp4cS3kgfTb7Sc8QB-KqrMY")

# Replace with your connection string
connection_string = "DefaultEndpointsProtocol=https;AccountName=digiimagesstorageuat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

# Replace with your container name
container_name = "ocrbulkimages"


# Initialize the BlobServiceClient
blob_service_client = BlobServiceClient.from_connection_string(connection_string)

# SQL Server database connection
db_connection = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};'
                               'SERVER=tcp:diginoticedbserver.database.windows.net;'
                               'DATABASE=diginoticedb;'
                               'UID=DigiNoticeAdmin;'
                               'PWD=Wp@75001;'
                               'Connection Timeout=0;')

async def insert_image_urls_to_db(image_urls, is_banking):
    try:
        db_connections = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};'
                               'SERVER=tcp:diginoticedbserver.database.windows.net;'
                               'DATABASE=diginoticedb;'
                               'UID=DigiNoticeAdmin;'
                               'PWD=Wp@75001;'
                               'Connection Timeout=0;')

        cursor = db_connections.cursor()
        # Call the appropriate stored procedure based on is_banking flag
        if is_banking:
            print("Inserting into tblAiBankingNotices")
            cursor.execute("{CALL Usp_insert_image_urls_to_tblAiBankingNotices(?)}", (json.dumps(image_urls),))
        else:
            print("Inserting into tblOCRBulkNotices")
            cursor.execute("{CALL Usp_insert_image_urls_to_tblOCRBulkNotices(?)}", (json.dumps(image_urls),))
        db_connections.commit()
        cursor.close()
        db_connections.close()
        return True
    except Exception as e:
        print(f"Database error: {e}")
        return False


@app.route('/', methods=['GET'])
def home():
    return jsonify({"message": "Hello Diginotice"})


# Write code to upload images to Azure Blob Storage
@app.route('/upload_image', methods=['POST'])
async def upload_image():
    print("upload_image")
    if 'images' not in request.files:
        return jsonify({"error": "No image files provided"}), 400

    images = request.files.getlist('images')
    userid = request.form.get('userId')
    noticeTypeId = request.form.get('noticeTypeId')
    newsPaperId = request.form.get('newsPaperId')
    publishedDate = request.form.get('publishedDate')
    is_banking = int(request.form.get('isBanking', 0))  # Default to 0 if not provided

    # Handle isPropertyNotice as both boolean and integer
    is_property_notice_raw = request.form.get('isPropertyNotice', '0')
    print(f"Raw isPropertyNotice value: '{is_property_notice_raw}' (type: {type(is_property_notice_raw)})")

    if isinstance(is_property_notice_raw, str):
        # Handle string representations of boolean
        if is_property_notice_raw.lower() == 'true':
            is_property_notice = 1
        elif is_property_notice_raw.lower() == 'false':
            is_property_notice = 0
        else:
            try:
                is_property_notice = int(is_property_notice_raw)
            except ValueError:
                is_property_notice = 0
    else:
        # Handle direct boolean or integer values
        is_property_notice = 1 if is_property_notice_raw else 0

    stateId = request.form.get('stateId', 0)# Default to 0 if not provided
    state = request.form.get('state')  # Default to empty string if not provided

    # Debug logging
    print(f"Processed parameters - isBanking: {is_banking}, isPropertyNotice: {is_property_notice}")
    print(f"All form data: {dict(request.form)}")

    uploaded_files = []
    folder_name = 'AiBankingBulkUploadedImages' if is_banking else 'ocrBulkuploadedImages'

    for image in images:
        # Generate a unique blob name
        current_date = datetime.now().strftime("%Y%m%d")
        unique_id = str(uuid.uuid4())

        # Construct blob name with property notice suffix when isBanking is 0
        if is_banking == 0:
            property_suffix = "PropertyNotice" if is_property_notice == 1 else "NonPropertyNotice"
            blob_name = f"{folder_name}/{stateId}_{current_date}_{unique_id}_{property_suffix}_{image.filename}"
            print(f"Non-banking blob name: {blob_name}")
        else:
            blob_name = f"{folder_name}/{stateId}_{current_date}_{unique_id}_{image.filename}"
            print(f"Banking blob name: {blob_name}")
        blob_url = "https://digiimagesstorageuat.blob.core.windows.net/ocrbulkimages/" + blob_name

        try:
            # Construct the base uploaded file object
            uploaded_file = {
                "ImageUrl": blob_url,
                "UserId": userid,
                "NoticeTypeId": noticeTypeId,
                "NewsPaperId": newsPaperId,
                "PublishedDate": publishedDate,
                "StateId": stateId,
                "State": state
            }

            # Add isPropertyNotice flag only when isBanking is 0
            if is_banking == 0:
                uploaded_file["isPropertyNotice"] = is_property_notice

            uploaded_files.append(uploaded_file)
            db_insert_success = await insert_image_urls_to_db(uploaded_files, is_banking)
            if not db_insert_success:
                # If the DB insertion fails, handle accordingly
                return jsonify({"error": "Failed to insert image URL into DB"}), 500
            else:
                uploaded_files = []

            # Create a blob client using the unique blob name
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

            # Upload the created file
            blob_client.upload_blob(image)
            # Generate the blob URL
            blob_url = blob_client.url
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    return jsonify({"message": "Images uploaded successfully"}), 200

loop = asyncio.new_event_loop()

def start_background_loop(loop):
    asyncio.set_event_loop(loop)
    loop.run_forever()

t = threading.Thread(target=start_background_loop, args=(loop,), daemon=True)
t.start()
class BulkOcrApi(Resource):
    def get(self, userId):
        """
        Instead of starting a new threading.Thread, we schedule
        the run_task_async(...) on our global asyncio event loop.
        """
        future = asyncio.run_coroutine_threadsafe(self.run_task_async(userId), loop)

        current_time = datetime.now().strftime("%d-%m-%Y %H:%M:%S")
        logging.info(f"Task started at {current_time} by user {userId}")

        return {'message': 'Task started', 'time': current_time}

    async def run_task_async(self, userId):
        """
        Wrap the existing run_task (which is synchronous) in asyncio.to_thread
        so it runs in a background thread without blocking the event loop.
        """
        await asyncio.to_thread(self.run_task, userId)

    def run_task(self, userId):
        """
        3. Your original synchronous logic stays here,
           100% unchanged.
        """
        db_connection2 = None
        cursor = None
        try:
            # SQL Server database connection
            db_connection2 = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};'
                                            'SERVER=tcp:diginoticedbserver.database.windows.net;'
                                            'DATABASE=diginoticedb;'
                                            'UID=DigiNoticeAdmin;'
                                            'PWD=Wp@75001;'
                                            'Connection Timeout=0;')
            cursor = db_connection2.cursor()
            query = """
                SELECT Id, ImageUrl 
                FROM tblOCRBulkNotices 
                WHERE ISNULL(isProcessed,0) = 0 
                ORDER BY id DESC
            """
            cursor.execute(query)
            data = cursor.fetchall()
            logging.info(f"Fetched {len(data)} records from tblOCRBulkNotices")
        except Exception as e:
            logging.error(f"Error connecting to database or fetching data: {e}")
            return
        finally:
            if cursor:
                cursor.close()
                logging.info("Cursor closed")
            if db_connection2:
                db_connection2.close()
                logging.info("Database connection closed")

        # Continue only if data is fetched
        if data:
            for row in data:
                image_url = row[1]
                notice_id = row[0]
                logging.info(f"Processing image URL: {image_url}, Notice ID: {notice_id}")

                try:
                    # Fetch the image data from the URL
                    response = requests.get(image_url)
                    if response.status_code != 200:
                        logging.error(f"Failed to fetch image from URL: {image_url}")
                        continue  # Skip to the next iteration

                    file = Image.open(BytesIO(response.content))
                    content = response.content
                    image = vision.Image(content=content)
                    logging.info("Image data fetched and prepared for OCR")

                    # Perform text detection with Google Vision
                    vision_client = vision.ImageAnnotatorClient()
                    vision_response = vision_client.text_detection(image=image)
                    if vision_response.error.message:
                        logging.error(f"Google Vision API error: {vision_response.error.message}")
                        continue  # Skip to the next iteration

                    texts = vision_response.text_annotations
                    extracted_text = ' '.join(text.description for text in texts)
                    logging.info("Text extracted from image using Google Vision API")

                    # JSON template for extraction
                    json_format = '''
                    {
                      "id": 0,
                      "isPublisher": "false",
                      "isPaid": "false",
                      "isOcr": 1,
                      "noticeTypeId": 1,
                      "statusId": 1,
                      "landCategoryId": "1",
                      "paperId": "",
                      "sectorNo": " ",
                      "projectName": "",
                      "ownerFullName": "",
                      "advocateId": 0,
                      "personId": 0,
                      "state": "",
                      "district": "",
                      "taluka": "",
                      "villageName": "",
                      "countryId": 1,
                      "stateId": 72,
                      "cityId": "",
                      "talukaId": "",
                      "villageId": "",
                      "createdBy": "",
                      "notice_image": [],
                      "noticeDetailList": [
                        {
                          "surveyNumber": "",
                          "fullSurveyNumber": "",
                          "gatNumber": "",
                          "fullGatNumber": "",
                          "finalPlotNo": "",
                          "subPlotNo": "",
                          "privatePlotNo": "",
                          "catestrialSurveyNo": "",
                          "plotNumber": "",
                          "fullPlotNumber": "",
                          "ctsNumber": "",
                          "fullCtsNumber": "",
                          "area": "",
                          "unitTypeId": "",
                          "propertyArea": "",
                          "houseNo": "",
                          "tenementNo": "",
                          "factoryShedNo": "",
                          "industrialBuilding": "",
                          "grampanchayatNo": "",
                          "nagarPanchyatMilkatNo": "",
                          "complaintNoReportNo": "",
                          "glrNo": "",
                          "malmattaNo": "",
                          "corporationRegistrationNo": "",
                          "propertyCard": "",
                          "phaseNo": "",
                          "buildingNo": " ",
                          "flatShopNo": "",
                          "commencementCertificateNo": "",
                          "completionCertificateNo": "",
                          "shareCertificateNo": "",
                          "propertyNo": "",
                          "buildingName": "",
                          "flatNo": "",
                          "floorNo": "",
                          "constructedPropertyArea": "",
                          "propertyCardNo": ""
                        }
                      ],
                      "noticeTitle": "",
                      "isActive": true,
                      "imageUrl": "",
                      "userName": "",
                      "googleMapLink": "",
                      "borrowerName": "",
                      "advocateAddress": "",
                      "advocatePhone": "",
                      "noticePeriod": "",
                      "landMark": "",
                      "advocateName": "",
                      "publishedDate": "",
                      "otherDetails": [
                        {
                          "key": "value"
                        }
                      ]
                    }
                    '''

                    # Create a prompt for Generative AI
                    prompt = f"""
                    You are a Data Engineer tasked with extracting structured information from a text-based notice.

                    Key Extraction Guidelines:
                    1. Translation and Preprocessing
                    - Translate the notice into English
                    - Remove punctuations and prefixes (Mr., Mrs.) from names
                    - Perform NLP tasks: semantic analysis, syntactic parsing, coreference resolution

                    2. Name and Identifier Extraction
                    - Identify owners and survey numbers with precise mapping
                    - Capitalize first letters of names (First Name Last Name format)
                    - Ensure survey numbers are in the correct format without non-English characters

                    3. Property and Asset Handling
                    - Classify property type:
                      * constructed - for buildings
                      * plot - for land
                      * land - for agricultural land
                    - Handle complex ownership scenarios:
                      * Multiple assets under a single owner
                      * Multiple owners for a single asset
                      * Ensure each owner-asset combination is uniquely represented

                    4. Specific Field Extraction
                    - Extract complete numerical values (dates, plot numbers, sectors)
                    - Populate bank full name
                    - Include units for area measurements (sq m, sq ft, ha)
                    - Capture name changes with:
                      * Current Name (before change)
                      * Changed Name (after change)
                    - Add "Adv. " prefix to advocate names

                    5. Special Handling
                    - For housing societies, replace with "CHSL"
                    - Handle comma-separated identifiers separately
                    - Capture notice period duration
                    - Extract tabular data comprehensively

                    6. Output Requirements
                    - Provide JSON output using a specified format
                    - Populate all fields accurately
                    - Add any additional information to 'OtherDetails'
                    - If the survey number or any other field has a range, mention that like: _ to _
                    - Ensure no incomplete or incorrect values

                    --- Notice text: {extracted_text}

                    --- Provide the JSON output using the following format:
                    {json_format}

                    Critically: Return only the JSON output, with no additional explanation."""

                    logging.info("Prompt created for Generative AI")

                    # Example: Using genai (pseudocode; adjust according to your usage)
                    generation_config = {
                        "temperature": 0.1,
                        "top_p": 0.95,
                        "top_k": 40,
                        "max_output_tokens": 8192,
                    }
                    safety_settings = [
                        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
                    ]

                    # Initialize the Generative AI model
                    # (Replace with your actual model initialization)
                    model = genai.GenerativeModel(
                        model_name="gemini-2.0-flash-exp",
                        generation_config=generation_config,
                        safety_settings=safety_settings
                    )
                    convo = model.start_chat(history=[])
                    genai_response = convo.send_message(prompt)
                    logging.info("Received response from Generative AI model")

                    # Extract JSON output from Generative AI response
                    extracted_info = genai_response.text.strip()

                    try:
                        extracted_info = extracted_info.strip()
                        # Remove triple-backticks if present
                        if extracted_info.startswith("```json") and extracted_info.endswith("```"):
                            extracted_info = extracted_info[7:-3]
                        extracted_info_dict = json.loads(extracted_info)
                        logging.info("Extracted information parsed into JSON")
                    except (json.JSONDecodeError, TypeError) as e:
                        logging.error(f"Failed to parse JSON output from Generative AI: {e}")
                        continue  # Skip to the next iteration

                    state_name = extracted_info_dict.get("state")
                    district_name = extracted_info_dict.get("district")
                    taluka_name = extracted_info_dict.get("taluka")
                    village_name = extracted_info_dict.get("villageName")
                    other_details = extracted_info_dict.get("otherDetails")

                    # Convert otherDetails to a comma-separated string if needed
                    if isinstance(other_details, list):
                        # Example: [{"key": "value"}, {"foo": "bar"}] => "key:value, foo:bar"
                        formatted = []
                        for item in other_details:
                            if isinstance(item, dict):
                                for k, v in item.items():
                                    formatted.append(f"{k}:{v}")
                        extracted_info_dict["otherDetails"] = ", ".join(formatted)
                    else:
                        extracted_info_dict["otherDetails"] = ""

                    # Now do your location matching as before
                    db_connection3 = None
                    cursor = None
                    try:
                        db_connection3 = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};'
                                                        'SERVER=tcp:diginoticedbserver.database.windows.net;'
                                                        'DATABASE=diginoticedb;'
                                                        'UID=DigiNoticeAdmin;'
                                                        'PWD=Wp@75001;'
                                                        'Connection Timeout=0;')
                        cursor = db_connection3.cursor()
                        logging.info("Connected to database for location data")

                        vtcFromOCR = str(village_name) + ', ' + str(taluka_name) + ', ' + str(district_name)
                        logging.info(f"Constructed search term: {vtcFromOCR}")

                        query = """
                            SELECT V.Id AS 'Village Id', V.Name + ', ' + T.Name + ', ' + C.Name AS 'names'
                            FROM LuVillage AS V
                            INNER JOIN LuTaluka AS T ON V.TalukaId = T.Id
                            INNER JOIN LuCity AS C ON C.Id = T.CityId
                        """
                        cursor.execute(query)
                        location_data = cursor.fetchall()
                        logging.info("Fetched location data from database")

                        def search_names(data, search_term, threshold=90):
                            results = []
                            try:
                                search_term_no_spaces = search_term.replace(" ", "")
                                for rowdata in data:
                                    name = rowdata[1]  # 'names'
                                    name_no_spaces = name.replace(" ", "")
                                    if fuzz.token_sort_ratio(name_no_spaces, search_term_no_spaces) >= threshold:
                                        results.append(rowdata)
                            except Exception as e:
                                logging.error(f"An error occurred in search_names: {e}")
                            return results

                        matching_rows = search_names(location_data, vtcFromOCR)
                        logging.info(f"Matching rows: {matching_rows}")

                        if matching_rows:
                            village_id = matching_rows[0][0]
                            query = """
                                SELECT v.id AS villageId, t.Id AS talukaId, C.Id AS CityId
                                FROM LuVillage V
                                INNER JOIN LuTaluka T ON V.TalukaId = T.Id
                                INNER JOIN LuCity C ON C.Id = T.CityId
                                WHERE V.id = ? AND V.isActive = 1
                            """
                            cursor.execute(query, (village_id,))
                            result = cursor.fetchall()
                            if result:
                                extracted_info_dict["villageId"] = result[0][0]
                                extracted_info_dict["talukaId"] = result[0][1]
                                extracted_info_dict["cityId"] = result[0][2]
                                logging.info("Updated extracted_info_dict with location IDs")
                            else:
                                logging.warning("No results found for village ID query")
                        else:
                            logging.warning("No matching rows found in location data")

                        # Define the notice_image array
                        notice_image = [{
                            "id": 630799,
                            "noticeId": notice_id,
                            "assetId": None,
                            "userId": None,
                            "name": image_url,
                            "imageTypeId": 1,
                            "isDeleted": False,
                            "isAdded": False
                        }]
                        extracted_info_dict["notice_image"] = notice_image

                        # Prepare final JSON for DB
                        json_data = json.dumps(extracted_info_dict)
                        logging.info("Final JSON data prepared")

                        district = extracted_info_dict.get("district", "")
                        districtId = extracted_info_dict.get("cityId", 0)
                        taluka = extracted_info_dict.get("taluka", "")
                        talukaId = extracted_info_dict.get("talukaId", 0)
                        villageName = extracted_info_dict.get("villageName", "")
                        villageId = extracted_info_dict.get("villageId", 0)

                        sql_query = """
                            UPDATE tblOCRBulkNotices
                            SET NoticeJSON = ?, 
                                City = ?, 
                                CityId = ?, 
                                Taluka = ?, 
                                TalukaId = ?, 
                                Village = ?, 
                                villageId = ?, 
                                DateModified = GETDATE(), 
                                ModifiedBy = ?, 
                                isProcessed = 1
                            WHERE id = ?
                        """
                        params = (
                            json_data,     # NoticeJSON
                            district,      # City
                            districtId,    # CityId
                            taluka,        # Taluka
                            talukaId,      # TalukaId
                            villageName,   # Village
                            villageId,     # villageId
                            userId,        # ModifiedBy
                            notice_id      # WHERE clause
                        )
                        cursor.execute(sql_query, params)
                        db_connection3.commit()
                        logging.info(f"Updated tblOCRBulkNotices for notice ID: {notice_id}")

                    except Exception as e:
                        logging.error(f"An error occurred during database update: {e}")
                        error_messages = str(e)
                        if cursor and db_connection3:
                            try:
                                sql_query = """
                                    UPDATE tblOCRBulkNotices
                                    SET isError = 1,
                                        ErrorMessage = ?
                                    WHERE id = ?
                                """
                                params = (error_messages, notice_id)
                                cursor.execute(sql_query, params)
                                db_connection3.commit()
                                logging.info(f"Marked notice ID {notice_id} as error in tblOCRBulkNotices")
                            except Exception as ex:
                                logging.error(f"An error occurred while updating isError flag: {ex}")
                    finally:
                        if cursor:
                            cursor.close()
                            logging.info("Cursor closed")
                        if db_connection3:
                            db_connection3.close()
                            logging.info("Database connection closed")
                except Exception as e:
                    logging.error(f"An unexpected error occurred while processing notice ID {notice_id}: {e}")
        else:
            logging.info("No data to process")
        logging.info("Task completed")



# Endpoint to process image and extract text
@app.route('/process_image', methods=['POST'])
def process_image():
    cursor = None
    db_connection = None
    try:
        if 'file' not in request.files:
            logging.error("No file part in the request")
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files['file']

        if file.filename == '':
            logging.error("No selected file")
            return jsonify({"error": "No selected file"}), 400

        # Read the image data
        content = file.read()
        image = vision.Image(content=content)

        # Perform text detection with Google Vision
        response = vision_client.text_detection(image=image)
        if response.error.message:
            logging.error(f"Google Vision API error: {response.error.message}")
            return jsonify({"error": response.error.message}), 500

        texts = response.text_annotations
        extracted_text = ' '.join(text.description for text in texts)
        logging.info("Text extracted from image")

        # JSON template for extraction
        json_format = '''
        {
          "id": 0,
          "isPublisher": "false",
          "isPaid": "false",
          "isOcr": 1,
          "noticeTypeId": 1,
          "statusId": 1,
          "landCategoryId": "1",
          "paperId": "",
          "sectorNo": " ",
          "projectName": "",
          "ownerFullName": "",
          "advocateId": 0,
          "personId": 0,
          "state": "",
          "district": "",
          "taluka": "",
          "villageName": "",
          "countryId": 1,
          "stateId": 72,
          "cityId": "",
          "talukaId": "",
          "villageId": "",
          "createdBy": "",
          "notice_image": [],
          "noticeDetailList": [
            {
              "surveyNumber": "",
              "fullSurveyNumber": "",
              "gatNumber": "",
              "fullGatNumber": "",
              "finalPlotNo": "",
              "subPlotNo": "",
              "privatePlotNo": "",
              "catestrialSurveyNo": "",
              "plotNumber": "",
              "fullPlotNumber": "",
              "ctsNumber": "",
              "fullCtsNumber": "",
              "area": "",
              "unitTypeId": "",
              "propertyArea": "",
              "houseNo": "",
              "tenementNo": "",
              "factoryShedNo": "",
              "industrialBuilding": "",
              "grampanchayatNo": "",
              "nagarPanchyatMilkatNo": "",
              "complaintNoReportNo": "",
              "glrNo": "",
              "malmattaNo": "",
              "corporationRegistrationNo": "",
              "propertyCard": "",
              "phaseNo": "",
              "buildingNo": " ",
              "flatShopNo": "",
              "commencementCertificateNo": "",
              "completionCertificateNo": "",
              "shareCertificateNo": "",
              "propertyNo": "",
              "buildingName": "",
              "flatNo": "",
              "floorNo": "",
              "constructedPropertyArea": "",
              "propertyCardNo": ""
            }
          ],
          "noticeTitle": "",
          "isActive": true,
          "imageUrl": "",
          "userName": "",
          "googleMapLink": "",
          "borrowerName": "",
          "advocateAddress": "",
          "advocatePhone": "",
          "noticePeriod": "",
          "landMark": "",
          "advocateName": "",
          "publishedDate": "",
          "otherDetails": [
            {
              "key": "value"
            }
          ]
        }
        '''

        # Create a prompt
        # Create a prompt for Generative AI
        prompt = f"""
        You are a Data Engineer tasked with extracting structured information from a text-based notice.

        Key Extraction Guidelines:
        1. Translation and Preprocessing
        - Translate the notice into English
        - Remove punctuations and prefixes (Mr., Mrs.) from names
        - Perform NLP tasks: semantic analysis, syntactic parsing, coreference resolution

        2. Name and Identifier Extraction
        - Identify owners and survey numbers with precise mapping
        - Capitalize first letters of names (First Name Last Name format)
        - Ensure survey numbers are in the correct format without non-English characters

        3. Property and Asset Handling
        - Classify property type:
          * constructed - for buildings
          * plot - for land
          * land - for agricultural land
        - Handle complex ownership scenarios:
          * Multiple assets under a single owner
          * Multiple owners for a single asset
          * Ensure each owner-asset combination is uniquely represented

        4. Specific Field Extraction
        - Extract complete numerical values (dates, plot numbers, sectors)
        - Populate bank full name
        - Include units for area measurements (sq m, sq ft, ha)
        - Capture name changes with:
          * Current Name (before change)
          * Changed Name (after change)
        - Add "Adv. " prefix to advocate names

        5. Special Handling
        - For housing societies, replace with "CHSL"
        - Handle comma-separated identifiers separately
        - Capture notice period duration
        - Extract tabular data comprehensively

        6. Output Requirements
        - Provide JSON output using a specified format
        - Populate all fields accurately
        - Add any additional information to 'OtherDetails'
        - If the survey number or any other field has a range, mention that like: _ to _
        - Ensure no incomplete or incorrect values

        --- Notice text: {extracted_text}

        --- Provide the JSON output using the following format:
        {json_format}

        Critically: Return only the JSON output, with no additional explanation."""

        print(prompt)
        logging.info("Prompt created for Generative AI")

        # Set up the model configuration
        generation_config = {
            "temperature": 0.1,
            "top_p": 0.95,
            "top_k": 40,
            "max_output_tokens": 8192,
        }

        # Safety settings
        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
        ]

        # Initialize the Generative AI model
        model = genai.GenerativeModel(
            # model_name="gemini-pro",
            model_name="gemini-2.0-flash-exp",
            generation_config=generation_config,
            safety_settings=safety_settings
        )
        logging.info("Generative AI model initialized")
        # Start a chat with the model

        logging.info("Sending prompt to Generative AI model")
        print(prompt)

        convo = model.start_chat(history=[])
        genai_response = convo.send_message(prompt)
        logging.info("Received response from Generative AI model")

        # Extract JSON output from Generative AI response
        extracted_info = genai_response.text.strip()
        print("Extracted Info:", extracted_info)  # This might need validation/formatting to ensure it's proper JSON

        try:
            # Remove leading and trailing whitespaces
            extracted_info = extracted_info.strip()
            logging.info("Processing extracted information")

            # Remove leading and trailing triple quotes
            if extracted_info.startswith("```json") and extracted_info.endswith("```"):
                extracted_info = extracted_info[7:-3]
            extracted_info_dict = json.loads(extracted_info)
            logging.info("Extracted information parsed into JSON")
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON output from Generative AI: {e}")
            return jsonify({"error": "Failed to parse JSON output from Generative AI"}), 500
        except TypeError as e:
            logging.error(f"No data received from Generative AI: {e}")
            return jsonify({"error": "No data received from Generative AI"}), 500
        # print("Extracted Info:", extracted_info)

        # Fetch IDs for state, district, taluka, village
        db_connection4 = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};'
                                       'SERVER=tcp:diginoticedbserver.database.windows.net;'
                                       'DATABASE=diginoticedb;'
                                       'UID=DigiNoticeAdmin;'
                                       'PWD=Wp@75001;'
                                       'Connection Timeout=0;')
        cursor = db_connection4.cursor()

        state_name = extracted_info_dict.get("state")
        district_name = extracted_info_dict.get("district")
        taluka_name = extracted_info_dict.get("taluka")
        village_name = extracted_info_dict.get("villageName")
        other_details = extracted_info_dict.get("otherDetails")

        # Format 'otherDetails' as a comma-separated string
        formatted_other_details = ', '.join([f"{item['key']}:{item['value']}" for item in other_details])
        extracted_info_dict["otherDetails"] = formatted_other_details
        logging.info(f"Formatted otherDetails: {formatted_other_details}")

        print(formatted_other_details)

        vtcFromOCR =str(village_name) + ', ' + str(taluka_name) + ', ' + str(district_name)
        # load data into dataframe
        query = """
        SELECT V.Id AS 'Village Id', V.Name + ', ' + T.Name + ', ' + C.Name AS 'names'
        FROM LuVillage AS V
        INNER JOIN LuTaluka AS T ON V.TalukaId = T.Id
        INNER JOIN LuCity AS C ON C.Id = T.CityId
        """
        cursor.execute(query)
        data = cursor.fetchall()
        logging.info("Fetched data from database for village, taluka, city")

        # Fix the search_names function to handle list data
        def search_names(data, search_term, threshold=90):
            results = []
            try:
                search_term_no_spaces = search_term.replace(" ", "")
                for row in data:
                    name = row[1]  # 'names' field is at index 1
                    name_no_spaces = name.replace(" ", "")
                    if fuzz.token_sort_ratio(name_no_spaces, search_term_no_spaces) >= threshold:
                        results.append(row)
            except Exception as e:
                logging.error(f"An error occurred in search_names: {e}")
                print(f"An error occurred: {e}")
            return results

        matching_rows = search_names(data, vtcFromOCR)
        logging.info(f"Matching rows: {matching_rows}")

        print(matching_rows)
        if matching_rows:
            village_id = matching_rows[0][0]

            query = """
                                SELECT v.id AS villageId, t.Id AS talukaId, C.Id AS CityId
                                FROM LuVillage V
                                INNER JOIN LuTaluka T ON V.TalukaId = T.Id
                                INNER JOIN LuCity C ON C.Id = T.CityId
                                WHERE V.id = ? AND V.isActive = 1
                                """
            try:
                cursor.execute(query, (village_id,))
                result = cursor.fetchall()
                logging.info(f"Result from village ID query: {result}")

                if len(result) > 0:
                    extracted_info_dict["villageId"] = result[0][0]
                    extracted_info_dict["talukaId"] = result[0][1]
                    extracted_info_dict["cityId"] = result[0][2]
            except Exception as e:
                logging.error(f"An error occurred while executing the query: {e}")
                return jsonify({"error": "Database query error"}), 500
        else:
            logging.warning("No matching rows found in database for village, taluka, city")

    except Exception as e:
        logging.error(f"An error occurred during database processing: {e}")
        return jsonify({"error": "Database processing error"}), 500

    finally:
        # Ensure that resources are released
        try:
            if cursor:
                cursor.close()
                logging.info("Cursor closed")
            if db_connection:
                db_connection.close()
                logging.info("Database connection closed")
        except Exception as e:
            logging.error(f"An error occurred while closing resources: {e}")
        # Return the result in JSON format
    return jsonify({"extracted_info": extracted_info_dict})


api.add_resource(BulkOcrApi, "/process_bulk_image/<string:userId>")

# Run the Flask application
if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
